<abp-modal-header
  [title]="'Integrações'"
  (onCloseClick)="fecharModal()"
></abp-modal-header>
<section class="content" [busy]="carregandoIntegracaoBalancete">
  <div class="container-fluid">
    <ul class="nav nav-tabs" id="myTab" role="tablist">
      <li
        class="nav-item"
        role="presentation"
        *ngIf="
          guard.validarPermissaoAcessoPorFuncionalidade(
            PermissoesConsts.IntegradorBalancete
          )
        "
      >
        <a
          class="nav-link"
          [ngClass]="{ active: apresentarIntegracoes == true }"
          data-toggle="tab"
          (click)="habilitarAbaIntegracoes()"
          role="tab"
          aria-controls="profile"
          aria-selected="false"
          >Templates</a
        >
      </li>
      <li class="nav-item" role="presentation">
        <a
          class="nav-link"
          [ngClass]="{ active: apresentarImportacoes == true }"
          data-toggle="tab"
          (click)="habilitarAbaImportacoes()"
          role="tab"
          aria-controls="home"
          aria-selected="true"
          >Arquivos Padronizados</a
        >
      </li>
    </ul>
    <div *ngIf="apresentarImportacoes">
      <div class="container mr-0">
        <div class="row">
          <div class="col align-self-end pr-0">
            <div class="dropdown">
              <button
                class="btn btn-sm btn-primary dropdown-toggle float-right"
                type="button"
                id="dropdownMenuButton1"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                Modelos
              </button>
              <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton1">
                <li class="disabled ml-3" style="color: gray">
                  <b>Apuração</b>
                </li>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloBalancete',
                      'planilha modelo importação balancete'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Balancete
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ApuracaoPorCentroCusto
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloCentroCustoApuracao',
                      'planilha modelo importação de centro custo apuracao'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Centro de custo de apuração
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ApuracaoPorCentroCusto
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloCentroCustoRepositorio',
                      'planilha modelo importação centro custo repositorio'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Centro de custo de repositório
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloContaContabilApuracao',
                      'planilha modelo importação conta contabil apuração'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Conta contábil de apuração
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloContaContabilRepositorio',
                      'planilha modelo importação conta contabil repositório'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Conta contábil de repositório
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloLancamentoAjuste',
                      'planilha modelo importacao lancamento de ajuste'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Lançamento de ajuste
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloPagamentoDeducao',
                      'planilha modelo importacao pagamento deducao'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Pagamento de Dedução
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloSaldoInicial',
                      'planilha modelo importacao saldo inicial'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Saldo Inicial
                </a>

                <li class="disabled ml-3 mt-2" style="color: gray">
                  <b>Lucro da Exploração</b>
                </li>

                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloCentroLucro',
                      'planilha modelo importacao centro de lucro'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Centro de Lucro
                </a>

                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloIdentificadorAdicional',
                      'planilha modelo importacao identificador adicional'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Identificador Adicional
                </a>

                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloReceitaIncentivada',
                      'planilha modelo importacao receitas incentivadas'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Receitas Incentivadas
                </a>

                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloLancamentoAjusteLex',
                      'planilha modelo importacao lançamentos de ajustes (LEX)'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Lançamentos de ajustes (LEX)
                </a>

                <li class="disabled ml-3 mt-2" style="color: gray">
                  <b>ECF</b>
                </li>

                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ImportacaoContaParteB
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloContaParteB',
                      'planilha modelo importação de conta da parte b'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Conta da parte B
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.LancamentoEcd
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloLancamentoEcd',
                      'planilha modelo importação de lançamento da ecd'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Lançamento da ecd
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ImportacaoMovtoAdcExc
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloMovimentoAdcExcEcf',
                      'planilha modelo importação de movimentação adc/exc'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Movimentação Adc/Exc
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoMovimentacaoPresumidoEcf',
                      'planilha modelo importação de movimentação do presumido'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Movimentação do presumido
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ImportacaoMovtoPatPfBn
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloMovimentoPatPfBnEcf',
                      'planilha modelo importação de movimentação PAT/PFBN'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Movimentação PAT/PFBN
                </a>
                <a
                  class="dropdown-item"
                  *ngIf="
                    guard.validarPermissaoAcessoPorFuncionalidade(
                      PermissoesConsts.ImportacaoModeloComposicaoCusto
                    )
                  "
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloComposicaoCusto',
                      'planilha modelo importação de composição de custo'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Modelo de composição de custo
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaFontePagadoraEcf',
                      'planilha modelo importação fonte pagadora'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Fontes Pagadoras
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloBlocoV',
                      'planilha modelo importação do bloco V'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Modelo Derex (bloco V)
                </a>
                <a
                  class="dropdown-item"
                  (click)="
                    baixarPlanilhaImportacaoModelo(
                      'GetPlanilhaImportacaoModeloBlocoW',
                      'planilha modelo importação do bloco W'
                    )
                  "
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Modelo declaração país a país (bloco W)
                </a>
                <a
                  class="dropdown-item"
                  (click)="baixarPacoteBloco('X', 'pacote importação bloco X')"
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Pacote importação bloco X
                </a>
                <a
                  class="dropdown-item"
                  (click)="baixarPacoteBloco('Y', 'pacote importação bloco Y')"
                >
                  <i class="fas fa-download" style="color: #000646"></i>
                  Pacote importação bloco Y
                </a>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <form autocomplete="off" #Form="ngForm" class="form-importacoes">
        <div class="row">
          <div class="col text-center">
            <div
              class="container"
              appDnd
              (fileDropped)="manipularArquivoArrastado($event)"
            >
              <input
                type="file"
                multiple
                class="custom-file-input"
                #fileDropRefImportacao
                id="fileDropRefImportacao"
                (change)="manipularBrowserArquivos($event.target.files)"
              />
              <h5>Arraste <b>aqui</b> os arquivos para importação</h5>
              <label
                class="btn btn-sm btn-outline-secondary"
                for="fileDropRefImportacao"
                >Procurar arquivos</label
              >
            </div>
          </div>
        </div>
      </form>
      <div class="conteudo-modal importacoes">
        <div class="row">
          <div
            class="col-12 col-md-4"
            *ngFor="let arquivo of arquivos"
            style="margin-top: 1.2rem"
          >
            <table table-sm class="">
              <tbody>
                <tr class="linha-arquivo-importacao">
                  <td rowspan="2">
                    <i class="far fa-file-alt p-1 icone-arquivo-importacao"></i>
                  </td>
                  <td rowspan="1">
                    <span data-toggle="tooltip" [title]="arquivo.name">{{
                      arquivo.name | slice : 0 : 60
                    }}</span>
                    <p style="margin: 0">
                      {{ formatarTamanhoArquivo(arquivo?.size) }}
                    </p>
                  </td>
                </tr>
              </tbody>
            </table>
            <div class="row">
              <div class="col-md-11">
                <div class="progress">
                  <div
                    class="progress-bar progress-bar-animated"
                    role="progressbar"
                    [attr.aria-valuenow]="arquivo.progressoImportacao"
                    aria-valuemin="0"
                    aria-valuemax="100"
                    [style.width]="arquivo.progressoImportacao + '%'"
                    [ngClass]="{
                      'bg-danger': ehArquivoComErro(arquivo),
                      'bg-success': ehArquivoComSucesso(arquivo)
                    }"
                  ></div>
                </div>
              </div>
              <div class="col-md-1" style="padding-left: 0">
                <i
                  class="far fa-trash-alt p-1"
                  (click)="removerArquivo(arquivo)"
                ></i>
              </div>
            </div>
            <div
              class="alert alert-danger"
              role="alert"
              *ngIf="ehArquivoComErro(arquivo) && possuiMensagemErro(arquivo)"
              data-toggle="tooltip"
              [title]="arquivo.logImportacao"
              style="cursor: pointer"
            >
              {{
                arquivo.logImportacao.length > 60
                  ? (arquivo.logImportacao | slice : 0 : 60) + "..."
                  : arquivo.logImportacao
              }}
              <i class="fas fa-exclamation" style="padding-left: 0.3rem"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div *ngIf="apresentarIntegracoes">
      <form
        class="form-horizontal"
        autocomplete="off"
        #Form="ngForm"
        [formGroup]="integracaoForm"
      >
        <div class="form-row mt-3 mb-2">
          <div class="col-3 d-flex">
            <div class="input-group">
              <input
                type="text"
                class="form-control form-control-sm"
                [value]="getNomeArquivo()"
                disabled
              />
              <input
                type="file"
                #inputArquivoImportacao
                id="inputArquivoImportacao"
                class="form-control form-control-sm-file"
                (change)="manipularEntradaArquivo($event.target.files)"
                accept=".txt, .csv, .xlsx, .xls"
                multiple
                style="display: none"
              />
              <span
                data-toggle="tooltip"
                title="Utilizar os balancetes armazenados no TS-Drive"
                *ngIf="
                  !possuiArquivoImportado() &&
                  (configuracaoOrigemBalanceteTenant ==
                    EnumOrigemBuscaBalancete.OrigemTsDrive ||
                    configuracaoOrigemBalanceteTenant ==
                      EnumOrigemBuscaBalancete.OrigemTsDriveLocal)
                "
                (click)="abrirModalDriveIntegracao()"
                class="input-group-text btn btn-primary"
              >
                <i class="fas fa-cloud"></i>
              </span>
              <span
                data-toggle="tooltip"
                title="Utilizar os balancetes armazenados no seu computador"
                *ngIf="
                  !possuiArquivoImportado() &&
                  (configuracaoOrigemBalanceteTenant ==
                    EnumOrigemBuscaBalancete.OrigemLocal ||
                    configuracaoOrigemBalanceteTenant ==
                      EnumOrigemBuscaBalancete.OrigemTsDriveLocal)
                "
                (click)="inputArquivoImportacao.click()"
                class="input-group-text btn btn-primary"
                style="border-left: white 1px solid !important"
              >
                <i class="fas fa-laptop"></i>
              </span>
              <span
                *ngIf="possuiArquivoImportado()"
                class="input-group-text"
                (click)="removerArquivoSelecionado()"
              >
                <i class="fas fa-times"></i>
              </span>
            </div>
          </div>
          <div class="col-md-3">
            <select
              class="form-select form-select-sm"
              formControlName="templateIntegracaoBalanceteId"
              id="templateIntegracaoBalanceteId"
            >
              <option [ngValue]="null" selected="selected">
                Selecione o template de integração
              </option>
              <option
                *ngFor="let template of templatesIntegradorBalancete"
                [value]="template.id"
              >
                {{ template.apelido }}
              </option>
            </select>
            <span
              class="badge badge-warning"
              *ngIf="apresentarMensagemApuracaoComTemplateDiferente"
              >As apurações selecionadas têm templates difentes. Defina o
              template.</span
            >
          </div>
          <div class="col-md-2">
            <select
              class="form-select form-select-sm"
              formControlName="mesAPartir"
            >
              <option [ngValue]="null" selected="selected">
                Selecione o mês de início
              </option>
              <option *ngFor="let indice of mesesFiltro" [value]="indice">
                {{ utils.getMesAno(indice) }}
              </option>
            </select>
          </div>
          <div class="col">
            <button
              type="button"
              class="btn btn-sm btn-primary"
              (click)="baixarPreviaIntegracao()"
              [disabled]="!integracaoForm.valid"
            >
              Baixar prévia
              <i
                class="fas fa-download fa-sm"
                *ngIf="!carregandoIntegracaoTemplate"
              ></i>
              <i
                class="fas fa-circle-notch fa-spin"
                *ngIf="carregandoIntegracaoTemplate"
              ></i>
            </button>
          </div>
        </div>
      </form>
      <div class="row">
        <div class="col-md-10">
          <div class="col">
            <div *ngFor="let mensagem of arquivoBalanceteConvertido.mensagens">
              <i
                class="fas fa-times-circle erro mr-1"
                *ngIf="mensagem.tipo == 1"
              ></i>
              <i
                class="fas fa-exclamation-triangle alerta mr-1"
                *ngIf="mensagem.tipo == 2"
              ></i>
              <span class="mensagem-arquivo-balancete">{{
                mensagem.mensagem
              }}</span>
            </div>
          </div>
        </div>
        <div class="col align-self-end">
          <span
            class="badge badge-secondary float-right mr-3"
            *ngIf="apuracoesSelecionadasNaGrid?.length == 1"
            >{{ apuracoesSelecionadasNaGrid?.length }} apuração
            selecionada</span
          >
          <span
            class="badge badge-secondary float-right mr-3"
            *ngIf="apuracoesSelecionadasNaGrid?.length > 1"
            >{{ apuracoesSelecionadasNaGrid?.length }} apurações
            selecionadas</span
          >
        </div>
      </div>
      <app-grid-apuracao-integracao
        (eventoRegistrosSelecionados)="eventoRegistrosSelecionados($event)"
      ></app-grid-apuracao-integracao>
    </div>
  </div>
  <abp-modal-footer
    *ngIf="apresentarImportacoes"
    [saveDisabled]="!habilitarImportacao()"
    [saveLabel]="'Importar'"
    (onSaveClick)="importarArquivos()"
    (onCancelClick)="bsModalRef.hide()"
  ></abp-modal-footer>
  <abp-modal-footer
    *ngIf="apresentarIntegracoes"
    [saveDisabled]="!integracaoForm.valid"
    [saveLabel]="'Importar'"
    (onSaveClick)="importarIntegracao()"
    (onCancelClick)="bsModalRef.hide()"
  ></abp-modal-footer>
</section>
