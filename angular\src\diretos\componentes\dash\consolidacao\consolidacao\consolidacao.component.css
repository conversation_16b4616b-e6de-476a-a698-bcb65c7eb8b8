.card-laranja {
  background-color: #000646;
  color: white;
}

.card-cinza {
  background-color: #4d4d4f;
  color: white;
}

.card-claro {
  color: #4d4d4f;
}

.dimensoes-relatorios-padrao {
  padding-left: 3rem;
}

.margem-top-padrao {
  margin-top: 3rem !important;
}

.preview-card {
  max-height: 85%;
  margin-top: 1.4rem;
}

.periodo-card {
  border: solid;
  border-color: rgba(252, 144, 3, 0.863);
  color: rgba(252, 144, 3, 0.842);
  text-align: center;
  max-width: 5rem;
  height: 5rem;
}

.borda-periodo-label {
  padding-top: 1.4rem;
}

.droplist {
  border-radius: 0.3rem;
  max-height: 13rem;
  background-color: rgba(255, 255, 255, 0.675);
}

.card-droplist {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
  min-height: 10rem;
  max-height: 10rem;
}

.cursorPointer {
  cursor: pointer;
}

.align-circle {
  text-align: -webkit-center;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.cor-valor {
  color: #4d4d4f;
}

.margin-inputs {
  margin-left: 0.8rem;
}

.droplist-text {
  font-size: 0.9rem;
}

.texto-descricao-limitada {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.switch-tela-principal {
  font-size: 20px;
}

.form-check-label {
  margin-top: 0.25rem !important;
}

.input-filtro-apuracoes {
  border: 0;
  outline: 0;
  border-radius: 0;
  border-bottom: 1px solid #6c757d7a;
}

.linha-grupo-selecionada {
  background-color: #f8d4c0;
}

.linha-grupo {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5px 0 0 1em;
}

.linha-grupo:hover {
  background-color: #f8d4c0a6;
}

.linha-botao {
  display: flex;
  justify-content: end;
  padding-right: 0.6em;
  opacity: 0;
}

.fas:hover {
  color: #000646 !important;
}

.linha-grupo:hover .linha-botao {
  display: flex;
  opacity: 1;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.relatorio-desabilitado {
  opacity: 0.6;
  pointer-events: none;
}

.plus {
  font-size: 1.3em !important;
}

.btn {
  padding: 0 !important;
}

.grid {
  display: flex;
  width: 100%;
  justify-content: start;
  box-sizing: border-box;
}

.botao {
  padding: 8px 20px;
  display: inline-block;
  width: 100%;
  color: #000;
  cursor: pointer;
  border: none;
  background: #fff;
  transition: background-color 0.3s ease;
}

.grid > :first-child {
  border-radius: 5px 0 0 5px;
}
.grid > :last-child {
  border-radius: 0 5px 5px 0;
}

.botao:hover {
  background-color: #f0f0f0;
}

.selecionado {
  background: #000646 !important;
  color: white !important;
}

.card {
  background-color: #fff;
  color: #000;
  padding: 1em;
  overflow: hidden;
}

.tabela-div {
  overflow-x: auto;
}

table {
  width: 100%;
  background-color: white;
  color: black;
  position: relative;
  border-top: 1.5px solid #ddd;
}

th,
td {
  padding: 0.6em 2em;
  text-align: left;
  min-width: 200px;
}

th:first-child,
td:first-child {
  min-width: 300px;
  padding: 0.6em 1em;
  font-size: 0.9em;
  position: sticky;
  left: 0;
  z-index: 1000;
  background-color: #fff;
}

th:first-child::after,
td:first-child::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 1px;
  background-color: #ddd;
}

.cursor-pointer {
  display: block;
  cursor: pointer;
  white: 24px;
  height: 24px;
}

.detalhe {
  color: #000646;
  font-weight: bold;
}

.empresa {
  font-size: 0.9em;
  font-weight: bold;
}

.empresa span {
  display: block;
  font-size: 0.8em;
  font-weight: normal;
  color: #9e9e9e;
}

.icone-especial {
  width: 21px;
  height: 21px;
}

.fechar {
  position: sticky;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2em;
}

.fechar h3 {
  font-weight: bold;
  color: #000;
  font-size: 1.1em !important;
}

.fechar-botoes {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1em;
  cursor: pointer;
}

.tela-expandida {
  position: fixed;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  justify-content: start;
  width: 100vw;
  height: 100vh;
  background-color: #fff;
  overflow-y: auto;
  z-index: 9999;
  cursor: auto;
}

.div-icone {
  padding: 1em;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tabela-titulo {
  display: flex;
  justify-content: start;
  align-items: center;
}

.topo-tabela {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1.1em;
}

.tabela-footer {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1em;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  width: 100%;
}

.total {
  color: #000646 !important;
  font-weight: 900 !important;
}

.card-info {
  color: #888;
}

.conteudo-info {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 0.6em;
}

.fechar-botoes .btn-outline-primary {
  padding: 0.5em !important;
}

.position-absolute {
  position: absolute;
}

.text-right {
  text-align: right !important;
}

.sub-registro .coluna-fixa {
  padding-left: 2.5em !important;
}

.subsub-registro .coluna-fixa {
  padding-left: 4em !important;
}

.ativo {
  transform: rotate(180deg);
}

.registro-ativo td {
  border-bottom: 1px solid #ee9667 !important;
}

.sub-registro-ativo td,
.sub-registro-ativo {
  border-bottom: 1px solid #ee9667 !important;
}

.sub-registro-ativo .coluna-fixa {
  border-bottom: 1px solid #ee9667 !important;
}

.svg-icon {
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}

td,
th {
  border: 1px solid #ddd !important;
}
