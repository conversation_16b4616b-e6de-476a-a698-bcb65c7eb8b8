<nav
  class="main-header navbar navbar-expand navbar-white navbar-light p-0 pt-0"
  style="
    height: 9vh !important;
    background-color: white !important;
    margin-left: 0 !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  "
>
  <div class="row w-100">
    <div class="col d-flex pt-1 pl-3" (click)="abrirTelaPlataforma()">
      <div style="max-width: 12rem">
        <img
          src="assets/new-design/tech solutions.png"
          class="header-logo-img"
          height="34px"
          class="pl-3"
          alt="Tech Solutions Logo"
        />
      </div>
    </div>
    <div class="col-6 pr-1" style="align-self: center">
      <!-- <header-language-menu></header-language-menu> -->
      <!-- <header-processamento-menu></header-processamento-menu>
      <header-user-menu></header-user-menu> -->
      <div class="row justify-content-end">
        <div
          class="col coluna-direita-botao-cabecalho coluna-botao-cabecalho"
          (click)="abrirTelaTreinamento()"
          *ngIf="
            verificarTenantSite() && validarApresentacaoBotoesModuloDiretos()
          "
        >
          <i class="fab fa-youtube icone-header"></i>
          <span>TSFlix</span>
        </div>
        <div
          class="col coluna-direita-botao-cabecalho coluna-botao-cabecalho"
          (click)="abrirTelaDashboard()"
          *ngIf="
            validarApresentacaoBotoesModuloDiretos() &&
            permission.isGranted('PagesPortal.Dashboard')
          "
        >
          <i class="fas fa-chart-line icone-header"></i>
          <span>Dashboard</span>
        </div>
        <div
          class="col coluna-direita-botao-cabecalho coluna-botao-cabecalho pl-4 pr-4"
          (click)="abrirModalProcessamento()"
          *ngIf="validarApresentacaoBotoesModuloDiretos()"
        >
          <i class="fas fa-cog icone-header"></i>
          <span>Processamentos</span>
        </div>
        <div
          class="col coluna-direita-botao-cabecalho coluna-botao-cabecalho"
          (click)="abrirModalRelatorios()"
          *ngIf="validarApresentacaoBotoesModuloDiretos()"
        >
          <i class="far fa-file-excel icone-header"></i>
          <span>Relatórios</span>
        </div>
        <div
          class="col coluna-direita-botao-cabecalho coluna-botao-cabecalho"
          (click)="abriModalImportacoes()"
          *ngIf="validarApresentacaoBotoesModuloDiretos()"
        >
          <i class="fas fa-download icone-header"></i>
          <span>Importações</span>
        </div>

        <div class="col-1 text-center">
          <div dropdown>
            <a
              href="javascript:;"
              dropdownToggle
              class="user-menu-toggle"
              style="color: black; font-size: 1.5rem"
            >
              <i class="fas fa-user-circle user-icon"></i>
            </a>
            <div
              class="dropdown-menu dropdown-menu-right user-dropdown"
              *dropdownMenu
            >
              <div class="user-info">
                <span class="user-name">{{ loginNameTratado }}</span>
              </div>
              <hr class="dropdown-divider" />
              <a
                class="dropdown-item user-menu-item"
                [routerLink]="['update-password']"
              >
                <i class="fas fa-user-edit nav-icon"></i>
                <span>{{ "UpdatePassword" | localize }}</span>
              </a>
              <a
                class="dropdown-item user-menu-item logout-item"
                href="javascript:;"
                (click)="logout()"
              >
                <i class="fas fa-sign-out-alt nav-icon"></i>
                <span>{{ "Logout" | localize }}</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</nav>
