.icone-excluir {
  color: #f44336;
  margin-left: 8px;
}

.coluna-acoes {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.opcoes {
  list-style: none;
  margin: 0;
  padding: 0;
  z-index: 10;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.opcoes :hover {
  background-color: white;
}

.opcoesAtivas {
  visibility: inherit;
}

.opcoesInativas {
  visibility: hidden;
  display: table-column;
  position: fixed;
  top: 309px;
  left: 1106px;
}

.position-fixed {
  right: 25px;
  bottom: 50px;
}

.card {
  width: 20em;
  z-index: 9999;
}

.dropdown-menu {
  z-index: 10;
}

.position-relative {
  z-index: 9999;
}

.progress {
  border-radius: 5px;
  animation-duration: 0.1s !important;
}

.bg-info {
  background-color: #ebe5e2 !important;
  color: black !important;
}

.secao-inclucao-arquivo {
  border: dashed 2px #80808054;
  border-radius: 10px;
  margin-top: 2rem;
}

.nav-link {
  color: black !important;
}

.linha-selecionada {
  background-color: #cfe2ff !important;
}

.text-truncate {
  max-width: 50ch;
}

.row {
  margin-left: 0;
  margin-right: 0;
}

.fit-content {
  max-width: fit-content !important;
  max-height: fit-content !important;
  align-items: center !important;
  justify-content: space-evenly !important;
  display: flex !important;
}

.truncate {
  max-width: 100% !important;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.select-tag {
  width: 100%;
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.select-tag-area {
  min-width: 250px !important;
}

.form-select {
  outline: none !important;
}

.form-row > div {
  padding-left: 0 !important;
  padding-right: 15px !important;
}

table {
  -webkit-user-select: none;
  /* Safari */
  -ms-user-select: none;
  /* IE 10 and IE 11 */
  user-select: none;
  /* Standard syntax */
}

@media (max-width: 768px) {
  .select-tag-area {
    min-width: 100% !important;
  }
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

tr {
  cursor: default;
}

i {
  cursor: pointer;
}

.custom-ng-select {
  cursor: pointer !important;
}

.select-all {
  margin-left: 8px;
  margin-right: 4px;
}

.alerta-nc {
  font-size: smaller;
  margin-left: 10px;
  color: #000646;
}
