.periodo {
  margin: 1rem;
}

.card-body {
  padding-left: 0.8rem;
  padding-right: 0.8rem;
  padding-top: 0.75rem !important;
  padding-bottom: 0.25rem !important;
}

.periodo-inativo {
  pointer-events: none;
  opacity: 0.5;
  background: #ccc;
}

.card-header {
  text-align: center;
  font-size: 15px;
  padding: 0.3rem;
  border: 1px solid grey;
  color: #141619;
  background-color: #d3d3d4;
}

.card-body {
  border-color: grey;
  border-style: solid;
  border-top: none;
  border-width: 1px;
}

.icone-editar {
  color: #141619;
  position: absolute;
  right: 0.5rem;
  top: 0.5rem;
  cursor: pointer;
}

.icone-bloqueio {
  color: #141619;
  position: absolute;
  right: 2rem;
  top: 0.5rem;
  cursor: pointer;
}

.descricaoHeader {
  color: gray;
  align-self: flex-end;
  margin-bottom: 0;
}

.card-mes {
  padding: 0 !important;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.form-check-input {
  font-size: 17px;
}

.form-check-label {
  padding-top: 3px;
}

.alert-warning {
  color: #383d41;
  background-color: #fff3cd;
  border-color: #ffecb5;
  text-align: center;
  padding: 6px;
}
