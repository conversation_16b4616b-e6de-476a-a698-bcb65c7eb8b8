.tag-beta {
  position: absolute;
  top: -0.5rem;
  right: -0.5rem;
}

/* Melhorias nos cards da página principal */
.btn-primary {
  background-color: #000646 !important;
  border: none !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 6, 70, 0.15), 0 2px 4px rgba(0, 6, 70, 0.1) !important;
  transition: all 0.3s ease !important;
  padding: 1.5rem !important;
  margin-right: 1rem !important;
}

.btn-primary:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 20px rgba(0, 6, 70, 0.2), 0 4px 8px rgba(0, 6, 70, 0.15) !important;
  background-color: #000856 !important;
}

.btn-primary:active {
  transform: translateY(0) !important;
}

/* Melhorias no layout da página */
.container-fluid {
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.content {
  padding: 2rem !important;
}

h2 {
  color: #2c3e50 !important;
  font-weight: 600 !important;
  margin-bottom: 0.5rem !important;
}

h4 {
  color: #6c757d !important;
  font-weight: 400 !important;
  margin-bottom: 3rem !important;
}

/* Estilo para o badge beta */
.badge-info {
  background-color: #17a2b8 !important;
  border-radius: 12px !important;
  font-size: 0.7rem !important;
  padding: 0.3rem 0.6rem !important;
}

/* Estilo para o loading */
.loading-icon {
  font-size: 2rem !important;
  color: #000646 !important;
}
