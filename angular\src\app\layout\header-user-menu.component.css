/* <PERSON><PERSON><PERSON> para o dropdown do usuário */
:host .user-image {
  width: 2.1rem !important;
  height: 2.1rem !important;
  border: 2px solid rgba(0, 6, 70, 0.1) !important;
  transition: all 0.2s ease !important;
}

:host .user-menu-toggle:hover .user-image {
  border-color: rgba(0, 6, 70, 0.3) !important;
  transform: scale(1.05) !important;
}

:host .user-dropdown {
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px !important;
  padding: 8px 0 !important;
  min-width: 200px !important;
  margin-top: 8px !important;
}

:host .user-info {
  padding: 12px 16px 8px 16px !important;
  background-color: rgba(0, 6, 70, 0.02) !important;
}

:host .user-label {
  display: block !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.6) !important;
  font-size: 0.7rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  margin-bottom: 4px !important;
}

:host .user-name {
  display: block !important;
  font-weight: 600 !important;
  color: #000646 !important;
  font-size: 0.9rem !important;
}

:host .dropdown-divider {
  margin: 8px 0 !important;
  border-color: rgba(0, 0, 0, 0.08) !important;
}

:host .user-menu-item {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  margin: 2px 8px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  color: rgba(0, 0, 0, 0.8) !important;
  text-decoration: none !important;
}

:host .user-menu-item:hover {
  background-color: rgba(0, 6, 70, 0.05) !important;
  color: #000646 !important;
  transform: translateX(2px) !important;
}

:host .user-menu-item .nav-icon {
  font-size: 0.9rem !important;
  margin-right: 12px !important;
  width: 16px !important;
  text-align: center !important;
}

:host .logout-item:hover {
  background-color: rgba(220, 53, 69, 0.05) !important;
  color: #dc3545 !important;
}
