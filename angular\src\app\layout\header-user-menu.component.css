/* Est<PERSON> para o dropdown do usu<PERSON>rio */
.user-image {
  width: 2.1rem;
  height: 2.1rem;
  border: 2px solid rgba(0, 6, 70, 0.1);
  transition: all 0.2s ease;
}

.user-menu-toggle:hover .user-image {
  border-color: rgba(0, 6, 70, 0.3);
  transform: scale(1.05);
}

.user-dropdown {
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 2px 6px rgba(0, 0, 0, 0.05) !important;
  border-radius: 8px !important;
  padding: 8px 0 !important;
  min-width: 200px !important;
  margin-top: 8px !important;
}

.user-info {
  padding: 12px 16px 8px 16px;
  background-color: rgba(0, 6, 70, 0.02);
}

.user-label {
  display: block;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.7rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.user-name {
  display: block;
  font-weight: 600;
  color: #000646;
  font-size: 0.9rem;
}

.dropdown-divider {
  margin: 8px 0 !important;
  border-color: rgba(0, 0, 0, 0.08) !important;
}

.user-menu-item {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  margin: 2px 8px !important;
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  color: rgba(0, 0, 0, 0.8) !important;
  text-decoration: none !important;
}

.user-menu-item:hover {
  background-color: rgba(0, 6, 70, 0.05) !important;
  color: #000646 !important;
  transform: translateX(2px);
}

.user-menu-item .nav-icon {
  font-size: 0.9rem;
  margin-right: 12px;
  width: 16px;
  text-align: center;
}

.logout-item:hover {
  background-color: rgba(220, 53, 69, 0.05) !important;
  color: #dc3545 !important;
}
