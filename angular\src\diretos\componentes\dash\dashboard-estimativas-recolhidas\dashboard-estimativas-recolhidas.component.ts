import { CurrencyPipe } from "@angular/common";
import { Component, Injector, OnInit } from "@angular/core";
import { Apuracao } from "@diretos/entidades/apuracao";
import { ListaGrafico } from "@diretos/entidades/dashboard";
import { EnumFormaApuracao } from "@diretos/enums/enum-forma-aputacao";
import { DashboardServer } from "@diretos/servicos/dashboard-server";
import { Utils } from "@diretos/utils/utils";
import { AppComponentBase } from "@shared/app-component-base";
import { Chart, registerables } from "chart.js";

@Component({
  selector: "app-dashboard-estimativas-recolhidas",
  templateUrl: "./dashboard-estimativas-recolhidas.component.html",
  styleUrls: ["./dashboard-estimativas-recolhidas.component.css"],
})
export class DashboardEstimativasRecolhidasComponent
  extends AppComponentBase
  implements OnInit
{
  _apuracaoId: number;
  _dadosGraficoEstimativaRecolhida: ListaGrafico[];
  _dadosApuracao: Apuracao;
  _periodoInicio: number;
  _periodoFim: number;
  _tipoCalculo: number;
  _tituloModal: string;
  utils = new Utils();

  constructor(
    injector: Injector,
    private _currency: CurrencyPipe,
    private _dashboardServer: DashboardServer
  ) {
    super(injector);
  }
  async ngOnInit(): Promise<void> {
    Chart.register(...registerables);
    this.getDadosGraficoEstimativaRecolhida(this._apuracaoId);
  }

  getDadosGraficoEstimativaRecolhida(apuracaId: number) {
    this._dashboardServer
      .getDadosGraficoEstimativaRecolhida(
        apuracaId,
        this._periodoInicio,
        this._periodoFim,
        this._tipoCalculo
      )
      .toPromise()
      .then((result) => {
        this._dadosGraficoEstimativaRecolhida = result;
        this.formatarDadosGraficoEstimativaRecolhida();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      });
  }

  formatarDadosGraficoEstimativaRecolhida() {
    const dadosReceitaBruta = [];
    const labels = [];

    this._dadosGraficoEstimativaRecolhida
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((imposto) => {
        dadosReceitaBruta.push({
          value: imposto.valor,
          label:
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(imposto.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  imposto.ordem.toString()
                ),
        });
        labels.push(
          this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
            ? this.utils.getMesAno(imposto.mes)
            : this.utils.getDescricaoEnumTrimestreLabel(
                imposto.ordem.toString()
              )
        );
      });

    this.criarRelatorioEstimativaRecolhida(dadosReceitaBruta, labels);
  }

  criarRelatorioEstimativaRecolhida(dadosReceitaBruta, labels) {
    const currencyPipe = this._currency;
    const data = [];
    const listaCoresColunasGrafico = [];
    let total = 0;
    for (let i = 0; i < dadosReceitaBruta.length; i++) {
      const vStart = total;
      total += dadosReceitaBruta[i].value;
      data.push([vStart, total]);
      if (dadosReceitaBruta[i].value < 0)
        listaCoresColunasGrafico.push("#f3d2be");
      else listaCoresColunasGrafico.push("#000646");
    }
    data.push(total);
    const backgroundColors = data.map(
      (o, i) => "rgba(0, 0, 0, " + i * 0.1 + ")"
    );

    new Chart("RelatorioEstimativaRecolhida", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            label: "Estimativa Recolhida",
            data: data,
            backgroundColor: listaCoresColunasGrafico,
            barPercentage: 1,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        animation: {
          duration: 0.1,
          onComplete: function () {
            var chart = this;
            var ctx = chart.ctx;
            ctx.textAlign = "center";
            ctx.textBaseline = "bottom";

            this.data.datasets.forEach(function (dataset, i) {
              var meta = chart.getDatasetMeta(i);
              meta.data.forEach(function (bar, index) {
                let valor = "0";
                if (!data[index].value) return null;
                else
                  valor = currencyPipe.transform(
                    data[index].value.toString(),
                    " ",
                    "symbol"
                  );
                ctx.fillText(valor, bar.x, bar.y - 5);
              });
            });
          },
        },
        plugins: {
          tooltip: {
            yAlign: "bottom",
            callbacks: {
              label: function (tooltipItem) {
                return currencyPipe.transform(
                  data[tooltipItem.dataIndex].value.toString(),
                  " ",
                  "symbol"
                );
              },
            },
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  customizeLabel(args) {
    return `${(args.percent * 100).toFixed(2)}%`;
  }

  customizeTooltip = (args: any) => ({
    text: `${args.argumentText}: ${this._currency.transform(args.valueText)}`,
  });
}

export class ColunasRelatorio {
  descricao: string;
  valor: any;
  color: string;
}
