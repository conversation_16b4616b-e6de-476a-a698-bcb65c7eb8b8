section,
.container-fluid {
  overflow: hidden;
}

.top-bar {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 1rem;
}

.fa-copy {
  color: black;
}

tr {
  cursor: default;
}

.far,
.fas,
.form-check {
  cursor: pointer;
  margin: 0;
  padding: 0;
}

.table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
}

.table th,
.table td {
  text-align: left;
  padding: 10px;
  vertical-align: middle;
}

.table-body-scroll {
  max-height: 400px;
  overflow-y: auto;
}

.coluna-acoes {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: 100%;
  height: max-content;
}

small.criador {
  color: #6c757d;
  font-size: 12px;
}

.card {
  max-height: 84vh;
  display: flex;
  flex-direction: column;
}

.table-body-scroll {
  flex-grow: 1;
  max-height: calc(84vh - 100px);
  overflow-y: auto;
  padding-bottom: 20px;
}
