import {
  ChangeDetectionStrategy, ChangeDetectorRef, Component, Injector, OnInit, Renderer2
} from '@angular/core';
import { BannerApresentacaoModuloComponent } from '@app/Plataforma/componentes/banner-apresentacao-modulo/banner-apresentacao-modulo.component';
import { ModalSustentacaoComponent } from '@app/Plataforma/componentes/modal-sustentacao/modal-sustentacao.component';
import { ModuloRoadmapComponent } from '@app/Plataforma/componentes/modulo-roadmap/modulo-roadmap.component';
import { EnumAbaMenuDiretos } from '@diretos/componentes/compartilhado/menu-superior/menu-superior.component';
import { NavigationEnd } from '@node_modules/@angular/router';
import { AbpSessionService } from '@node_modules/abp-ng2-module';
import { Observable } from '@node_modules/rxjs/dist/types';
import { AppComponentBase } from '@shared/app-component-base';
import { LayoutStoreService } from '@shared/layout/layout-store.service';
import { MenuItem } from '@shared/layout/menu-item';
import { EnumModulo } from '@shared/plataforma/enums/enum-modulos';
import { EnumTipoContrato } from '@shared/service-proxies/service-proxies';
import { TenantModuleServer } from '@shared/service-proxies/tenant-modulo-server';
import { AppSessionService } from '@shared/session/app-session.service';
import { delay, filter, map } from 'rxjs/operators';
import { fromEvent } from 'rxjs';
import { CompartilhamentoMensagemService } from '@diretos/servicos/compartilhamento-mensagem-server';
import { EnumAbaMenuDrive } from '@drive/componentes/compartilhado/menu-superior/menu-superior.component';
import { PermissoesDiretos } from '@diretos/enums/enum-permissoes';


@Component({
  // tslint:disable-next-line:component-selector
  selector: 'sidebar',
  templateUrl: './sidebar.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  styleUrls: ['./sidebar.component.css']
})
export class SidebarComponent extends AppComponentBase implements OnInit {

  homeRoute = '/app/plataforma';
  statusMenuLateral;
  collapseSubMenuConta = true;
  collapseSubMenuDiretos = true;
  collapseSubMenuDrive = true;
  collapseSubMenuTenants = true;
  EnumStatusMenuLateral = EnumStatusMenuLateral;
  EnumAbaMenuDiretos = EnumAbaMenuDiretos;
  EnumAbaMenuDrive = EnumAbaMenuDrive;
  EnumModulo = EnumModulo
  PermissoesDiretos = PermissoesDiretos;

  constructor(
    injector: Injector,
    private appSessionService: AppSessionService,
    private _tenantModuleServer: TenantModuleServer,
    private _sessionService: AbpSessionService,
    private renderer: Renderer2,
    private cd: ChangeDetectorRef,
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.consultarOpcaoSalvaMenuLateralPorUsuario();

    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(async (event: NavigationEnd) => {
      this.cd.detectChanges();
    });

    if (this.router.url.includes('diretos')) {
      this.collapseSubMenuDiretos = false;
      this.cd.detectChanges();
    }
    if (this.router.url.includes('drive')) {
      this.collapseSubMenuDrive = false;
      this.cd.detectChanges();
    }
    if (this.router.url.includes('app/plataforma/')) {
      this.collapseSubMenuConta = false;
      this.collapseSubMenuTenants = false;
      this.cd.detectChanges();
    }
  }

  tratarAppPathDaRota(rota: string) {
    if (abp.appPath == "/")
      return rota;
    else
      return `${abp.appPath}${rota}`;
  }

  verificarApresentacaoModulo(modulo: number) {
    return this._sessionService.tenantId;
  }

  verificarSePossuiModuloAtivado(modulo: EnumModulo) {
    return this.appSession?._modulos?.find(x => x.modulo == modulo)?.ativo;
  }

  abrirBannerApresentacaoModulo(modulo: EnumModulo) {
    this.modalService.show(BannerApresentacaoModuloComponent, {
      class: 'modal-md',
      initialState: {
        _modulo: modulo,
      },
    });
  }

  async acessarModulo(modulo: EnumModulo, subMenuDiretos?: EnumAbaMenuDiretos, subMenuDrive?: EnumAbaMenuDrive) {
    let iconeLoader = document.getElementById(`icone-loader-modulo-${modulo}`);
    iconeLoader.style.display = "inline-block";
    await this._tenantModuleServer.verificarModuloDisponivel(modulo).toPromise()
      .then(() => {
        if (modulo == EnumModulo.TsDiretos)
          this.acessarSubMenuDiretos(subMenuDiretos)
        if (modulo == EnumModulo.TsDrive)
          this.acessarSubMenuDrive(subMenuDrive)
      })
      .catch(() => this.message.warn('Tente novamente em alguns minutos.', 'Este módulo não está disponível no momento.'))
      .finally(() => iconeLoader.style.display = "none")
  }

  acessarSubMenuDiretos(submenu: EnumAbaMenuDiretos) {
    localStorage.setItem('abaSelecionadaMenuDiretos', submenu.toString())
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      if (submenu == EnumAbaMenuDiretos.dashboard)
        this.router.navigateByUrl(this.tratarAppPathDaRota('/diretos/dashboard'));
      else
        this.router.navigateByUrl(this.tratarAppPathDaRota('/diretos/home'));
    });
  }

  acessarSubMenuDrive(submenu: EnumAbaMenuDrive) {
    localStorage.setItem('abaSelecionadaMenuDrive', submenu.toString())
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl(this.tratarAppPathDaRota('/drive/home'));
    });
  }

  verficarSubItemDiretosSelecionado(subMenuSelecionado: EnumAbaMenuDiretos) {
    let abaSelecionadaMenuDiretos = localStorage.getItem('abaSelecionadaMenuDiretos')

    //Está na tela inicial do módulo Diretos
    if (this.router.url.includes("diretos/home")) {
      return abaSelecionadaMenuDiretos == subMenuSelecionado.toString()
    }

    //Está acessando uma apuração
    if (this.router.url.includes("diretos/apuracao/") && subMenuSelecionado == EnumAbaMenuDiretos.apuracao)
      return true;

    //Está acessando os dashboard
    if (this.router.url.includes("diretos/dashboard") && subMenuSelecionado == EnumAbaMenuDiretos.dashboard)
      return true;

    //Está acessando uma ECF
    if (this.router.url.includes("diretos/ecf/") && subMenuSelecionado == EnumAbaMenuDiretos.ecf)
      return true;

    //Está acessando repositórios ou integradores
    return this.router.url.includes("diretos/home") && abaSelecionadaMenuDiretos == subMenuSelecionado.toString()
  }

  verficarSubItemDriveSelecionado(subMenuSelecionado: EnumAbaMenuDrive) {
    let abaSelecionadaMenuDrive = localStorage.getItem('abaSelecionadaMenuDrive')

    //Está na tela inicial do módulo Diretos
    if (this.router.url.includes("drive/home")) {
      return abaSelecionadaMenuDrive == subMenuSelecionado.toString()
    }

    //Está acessando uma apuração
    if (this.router.url.includes("drive/") && subMenuSelecionado == EnumAbaMenuDrive.meuDrive)
      return true;

    //Está acessando os dashboard
    if (this.router.url.includes("drive/") && subMenuSelecionado == EnumAbaMenuDrive.favorito)
      return true;

    //Está acessando uma ECF
    if (this.router.url.includes("drive/") && subMenuSelecionado == EnumAbaMenuDrive.integracao)
      return true;

    return false;
  }

  abrirModalAjuda() {
    return this.modalService.show(ModalSustentacaoComponent, {
      class: 'modal-lg',
    });
  }

  verificarSePossuiPermissaoDeAcesso(descricaoPermissao: string, rota: string): boolean {
    if ((rota.includes('diretos') || rota.includes('drive')) && !this.appSession.tenant)
      return false;

    if (rota.includes('drive') && !this.verificarApresentacaoModulo(EnumModulo.TsDrive))
      return false;

    else {
      if (descricaoPermissao == "PagesPortal.TenantPlano" && this.appSessionService.tenant?.tenantPermissoes.tipoContrato == EnumTipoContrato.VendaExecutiva)
        return false;
      if (descricaoPermissao)
        return true;
      return this.permission.isGranted(descricaoPermissao);
    }
  }

  verificarSeMenuEstaAtivo(urlMenu) {
    return this.router.url == urlMenu;
  }

  consultarOpcaoSalvaMenuLateralPorUsuario() {
    if (localStorage.getItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`) &&
      localStorage.getItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`) == EnumStatusMenuLateral.aberto.toString()) {
      this.abrirMenuLateral()
    }
    else if (localStorage.getItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`) &&
      localStorage.getItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`) == EnumStatusMenuLateral.fechado.toString()) {
      this.fecharMenuLateral()
    }
    else
      this.abrirMenuLateral()
  }

  manipularClickIconeMenu(): void {
    if (this.statusMenuLateral == EnumStatusMenuLateral.aberto)
      this.fecharMenuLateral()
    else if (this.statusMenuLateral == EnumStatusMenuLateral.fechado)
      this.abrirMenuLateral()
  }

  abrirMenuLateral(): void {
    this.statusMenuLateral = EnumStatusMenuLateral.aberto
    this.renderer.removeClass(document.body, 'sidebar-collapse');
    this.renderer.addClass(document.body, 'sidebar-open');
    localStorage.setItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`, EnumStatusMenuLateral.aberto.toString());
  }

  fecharMenuLateral(): void {
    this.statusMenuLateral = EnumStatusMenuLateral.fechado
    this.renderer.removeClass(document.body, 'sidebar-open');
    this.renderer.addClass(document.body, 'sidebar-collapse');
    localStorage.setItem(`opcao_abertura_menu_lateral_${this.appSession.user.id}`, EnumStatusMenuLateral.fechado.toString());
  }

  verificarSeMenuEstaAberto() {
    return this.statusMenuLateral == EnumStatusMenuLateral.aberto;
  }

  vefificarAcessoHost() {
    return !this.appSession.tenant
  }

  verificarSePossuiPermissaoAcessoRotaDiretos(permissao: PermissoesDiretos) {
    return this.isGranted(permissao)
  }
}

export enum EnumStatusMenuLateral {
  aberto = 1,
  fechado = 2
}