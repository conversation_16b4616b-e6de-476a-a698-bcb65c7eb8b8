import { CurrencyPipe } from "@angular/common";
import { Component, Injector, OnInit } from "@angular/core";
import { Apuracao } from "@diretos/entidades/apuracao";
import { CardPfBn } from "@diretos/entidades/dashboard";
import { Utils } from "@diretos/utils/utils";
import { AppComponentBase } from "@shared/app-component-base";
import { Chart } from "chart.js";

@Component({
  selector: "app-dashboard-prejuizo-fiscal-base-negativa",
  templateUrl: "./dashboard-prejuizo-fiscal-base-negativa.component.html",
  styleUrls: ["./dashboard-prejuizo-fiscal-base-negativa.component.css"],
})
export class DashboardPrejuizoFiscalBaseNegativaComponent
  extends AppComponentBase
  implements OnInit
{
  _apuracaoId: number;
  _dadosApuracao: Apuracao;
  _dadosdPfBn: CardPfBn;
  _periodoInicio: number;
  _periodoFim: number;
  _tipoCalculo: number;
  _tituloModal: string;
  utils = new Utils();

  constructor(injector: Injector, private _currency: CurrencyPipe) {
    super(injector);
  }

  ngOnInit(): void {
    this.converteDados();
  }

  converteDados() {
    const valoresSaldoInicial = [];
    const valoresMovimantacao = [];
    const valoresSaldoFinal = [];
    let valoresSaldoInicialCascata = [];
    let valoresMovimentacaoCascata = [];
    let valoresSaldoFinalCascata = [];

    valoresSaldoInicial.push(
      this._dadosdPfBn.saldoInicialPf,
      this._dadosdPfBn.saldoInicialBn
    );
    valoresMovimantacao.push(
      this._dadosdPfBn.movimentoPf,
      this._dadosdPfBn.movimentoBn
    );
    valoresSaldoFinal.push(
      this._dadosdPfBn.saldoFinalPf,
      this._dadosdPfBn.saldoFinalBn
    );

    let movimentacaoPF = valoresSaldoInicial[0] + valoresMovimantacao[0];
    let movimentacaoBN = valoresSaldoInicial[1] + valoresMovimantacao[1];

    valoresSaldoInicialCascata.push(
      [0, valoresSaldoInicial[0]],
      [0, valoresSaldoInicial[1]]
    );
    valoresMovimentacaoCascata.push(
      [valoresSaldoInicial[0], movimentacaoPF],
      [valoresSaldoInicial[1], movimentacaoBN]
    );
    valoresSaldoFinalCascata.push(
      [0, valoresSaldoFinal[0]],
      [0, valoresSaldoFinal[1]]
    );

    this.criarRelatorioDadosdPfBn(
      valoresSaldoInicialCascata,
      valoresMovimentacaoCascata,
      valoresSaldoFinalCascata
    );
  }

  criarRelatorioDadosdPfBn(
    valoresSaldoInicial,
    valoresMovimantacao,
    valoresSaldoFinal
  ) {
    new Chart("RelatorioPfBn", {
      type: "bar",
      data: {
        labels: ["Prejuizo Fiscal", "Base negativa"],
        datasets: [
          {
            label: "Saldo Inicial",
            data: valoresSaldoInicial,
            backgroundColor: "#f8d4c0",
            order: 2,
            barPercentage: 0.5,
          },
          {
            label: "Movimento",
            data: valoresMovimantacao,
            backgroundColor: "grey",
            order: 2,
            barPercentage: 0.5,
          },
          {
            label: "Saldo Final",
            data: valoresSaldoFinal,
            backgroundColor: "#000646",
            order: 2,
            barPercentage: 0.5,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioCascata() as any,
    });
  }

  retornarDefinicoesRelatorioCascata() {
    const currency = this._currency;
    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 0.1,
        onComplete: function () {
          var chart = this;
          var ctx = chart.ctx;
          ctx.textAlign = "center";
          ctx.textBaseline = "bottom";

          this.data.datasets.forEach(function (dataset, i) {
            var meta = chart.getDatasetMeta(i);
          });
        },
      },
      plugins: {
        legend: {
          display: true,
        },
        tooltip: {
          yAlign: "bottom",
          callbacks: {
            label: function (tooltipItem) {
              if (tooltipItem.datasetIndex == 1) {
                let valorMovimentoInicial =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][0];
                let valorMovimentoFinal =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1];
                let valorDiferenca =
                  valorMovimentoFinal - valorMovimentoInicial;
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  valorDiferenca,
                  " ",
                  "symbol"
                )}`;
              } else
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1].toString(),
                  " ",
                  "symbol"
                )}`;
            },
          },
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };
  }

  customizeLabel(args) {
    return `${(args.percent * 100).toFixed(2)}%`;
  }

  customizeTooltip = (args: any) => ({
    text: `${args.argumentText}: ${this._currency.transform(args.valueText)}`,
  });
}

export class ColunasRelatorio {
  descricao: string;
  valor: any;
  color: string;
}
