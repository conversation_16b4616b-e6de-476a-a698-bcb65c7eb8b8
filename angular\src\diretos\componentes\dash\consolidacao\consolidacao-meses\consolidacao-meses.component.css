.grid-meses {
  display: flex;
  justify-content: start;
  width: 100%;
  box-sizing: border-box;
  margin: 1.5em 0;
}

.mes {
  padding: 8px 20px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #000;
  cursor: pointer;
  background: #fff;
  transition: background-color 0.3s ease;
}

.grid-meses > :first-child {
  border-radius: 5px 0 0 5px;
}
.grid-meses > :last-child {
  border-radius: 0 5px 5px 0;
}

.mes:hover {
  background-color: #f0f0f0;
}

.selecionado {
  background: #000646 !important;
  color: white !important;
}
