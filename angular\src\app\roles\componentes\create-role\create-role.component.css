.ativa {
  background-color: #f5f5f5;
  color: #000;
}

.desativado {
  color: #ccc;
}

.voltar:hover {
  text-decoration: underline !important;
  cursor: pointer;
}

.titulo-content {
  padding-bottom: 1em;
}

.flex {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.accordion-content {
  overflow: hidden;
  animation: slideDown 0.3s ease forwards;
}

@keyframes slideDown {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: auto;
    opacity: 1;
  }
}

button {
  background-color: #f0f0f0;
  border: none;
  color: #333;
  font-weight: bold;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 4px;
}

.modulo-item {
  border: 1px solid #ddd;
  padding: 4px 10px 4px 10px;
  margin: 10px 0;
  border-radius: 4px;
}

.titulo {
  cursor: pointer;
}

.titulo:hover {
  color: #000646;
}

.borda-topo {
  border-top: 1px solid #ddd;
  padding-top: 10px;
  margin-top: 10px;
}

.text-bold {
  font-weight: bold !important;
}

.select-arrow-down {
  background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDE2IDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEuNDE0MjksMC4yODU3MTYgTDEuNDE0MjksMS4yODU3MSBMOC4wMDAwMiw3Ljg1NzE2IEwxNC41ODU3LDEuMjg1NzEgTDE0LjU4NTcsMC4yODU3MTYgTDEzLjU4NTcsMC4yODU3MTYgTDgsNS43MTQyOSBMMi40MTQyOSwwLjI4NTcxNiBMMS40MTQyOSwwLjI4NTcxNiBaIiBmaWxsPSIjMDAwIi8+PC9zdmc+")
    no-repeat right;
  background-size: 12px 12px;
  padding-right: 20px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.disabled {
  background-color: #f5f5f5;
  color: #ccc;
}

.spacing-column:nth-child(2n) {
  padding-left: 1em !important;
}

.spacing-column:nth-child(2n + 1) {
  padding-right: 1em !important;
  border-right: 1px solid #ddd;
}

.form-switch:checked {
  background-color: #000646 !important;
}
