<div *ngIf="carregando" class="freeze-ui position-absolute" data-text=" "></div>
<form [formGroup]="dadosConsolidacaoForm">
  <div class="row">
    <div class="col-2 pt-3">
      <div class="droplist shadow">
        <div class="card pt-2">
          <div class="card-header d-flex pb-2 mb-2">
            <div class="col-8">
              <input
                type="text"
                class="form-control input-filtro-apuracoes"
                placeholder="Pesquisar"
                (keyup)="filtrarListaGrupos($event.target.value)"
              />
            </div>
            <div class="col mep ml-2">
              <button
                type="button"
                class="btn btn-outline-primary block w-100 plus"
                (click)="abrirModal()"
              >
                +
              </button>
            </div>
          </div>
          <div class="card-droplist">
            <div class="col-12 m-0 p-0 w-100">
              <div
                class="linha-grupo"
                *ngFor="let dado of retornarGruposFiltrados()"
                [id]="'grupo_' + dado.id"
                (click)="alterarGrupoSelecionado(dado.id)"
                [ngClass]="{
                  'linha-grupo-selecionada': grupoSelecionado == dado.id
                }"
              >
                <div
                  class="texto-descricao-limitada w-75"
                  data-toggle="tooltip"
                >
                  {{ dado.apelido }}
                </div>
                <div class="linha-botao w-25">
                  <button
                    type="button"
                    class="btn btn-sm"
                    data-toggle="tooltip"
                    title="Editar"
                    (click)="abrirModal(dado.id)"
                  >
                    <i class="fas fa-edit"></i>
                  </button>
                  <button
                    type="button"
                    class="btn btn-sm ml-2"
                    data-toggle="tooltip"
                    title="Excluir"
                    (click)="delete(dado.id)"
                  >
                    <i class="fas fa-trash-alt"></i>
                  </button>
                </div>
              </div>
              <hr class="mt-1 mb-2 ml-3 mr-2" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-10 pl-4">
      <div class="dimensoes-rela. torios-padrao pt-3">
        <div class="row">
          <div class="col-2">
            <select
              class="form-select w-100 h-100 form-control mep m-0"
              id="tipoCalculo"
              title="Tipo de Cálculo"
              formControlName="tipoCalculo"
              (change)="alterarTipoCalculo($event.target.value)"
            >
              <option *ngFor="let indice of tipoCalculoSelect" [value]="indice">
                {{ utils.getEnumTipoContaConsolidacaoIrpjCsllLabel(indice) }}
              </option>
            </select>
          </div>
          <div class="col-10">
            <div class="grid shadow">
              <button
                type="button"
                *ngFor="let item of botoes"
                class="botao"
                [ngClass]="{ selecionado: selecionado === item.id }"
                (click)="selecionarTipo(item.param)"
              >
                {{ item.name }}
              </button>
            </div>
          </div>
        </div>
        <app-consolidacao-meses
          [dadosConsolidacaoForm]="dadosConsolidacaoForm"
          (alterarMes)="alterarMes($event)"
        ></app-consolidacao-meses>
        <div
          *ngIf="grupoSelecionado && dadosTabela?.detalhes.length > 0"
          class="card p-0 shadow"
        >
          <div class="topo-tabela text-left">
            <div class="w-75 pl-3">Consolidação das Apurações</div>
            <div class="w-15 d-flex justify-content-end">
              <div class="div-icone">
                <svg
                  class="cursor-pointer icone-especial"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 21 20"
                  fill="#555"
                  (click)="expandirTela = !expandirTela"
                >
                  <path
                    d="M5.0457 1.36364V0H9.59115V4.54545H8.22752V2.32727L2.82752 7.72727H5.0457V9.09091H0.500244V4.54545H1.86388V6.76364L7.26388 1.36364H5.0457ZM20.4821 14.3818L19.9093 18.4364C19.7821 19.3364 19.0093 20 18.1093 20H12.5093C12.0275 20 11.3366 19.8091 11.0002 19.4636L6.86388 15.1091L7.61843 14.3455C7.83661 14.1273 8.1457 14.0273 8.45479 14.0909L11.4093 14.7636V5C11.4093 4.24545 12.0184 3.63636 12.773 3.63636C13.5275 3.63636 14.1366 4.24545 14.1366 5V10.4545H14.9639C15.2457 10.4545 15.5275 10.5182 15.773 10.6455L19.4912 12.5C20.1912 12.8545 20.5912 13.6091 20.4821 14.3818Z"
                    fill="#555"
                  />
                </svg>
              </div>
              <div class="div-icone">
                <svg
                  class="mr-2 cursor-pointer"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 21 20"
                  fill="#000"
                  (click)="baixarRelatorio()"
                >
                  <g clip-path="url(#clip0_121_771)">
                    <path
                      d="M15.4999 12.5V15H5.49992V12.5H3.83325V15C3.83325 15.9167 4.58325 16.6667 5.49992 16.6667H15.4999C16.4166 16.6667 17.1666 15.9167 17.1666 15V12.5H15.4999ZM14.6666 9.16671L13.4916 7.99171L11.3333 10.1417V3.33337H9.66658V10.1417L7.50825 7.99171L6.33325 9.16671L10.4999 13.3334L14.6666 9.16671Z"
                      fill="#000646"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_121_771">
                      <rect
                        width="20"
                        height="20"
                        fill="white"
                        transform="translate(0.5)"
                      />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
          <div
            class="tabela-div"
            [ngClass]="{ 'tela-expandida': expandirTela }"
          >
            <span *ngIf="expandirTela" class="fechar">
              <h3>Consolidação das Apurações</h3>
              <div class="fechar-botoes">
                <button
                  type="button"
                  class="btn btn-outline-primary"
                  (click)="baixarRelatorio()"
                >
                  Exportar Relatório
                </button>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="28"
                  height="28"
                  viewBox="0 0 96 96"
                  fill="none"
                  (click)="expandirTela = !expandirTela"
                >
                  <g clip-path="url(#clip0_451_93)">
                    <path
                      d="M76 25.64L70.36 20L48 42.36L25.64 20L20 25.64L42.36 48L20 70.36L25.64 76L48 53.64L70.36 76L76 70.36L53.64 48L76 25.64Z"
                      fill="black"
                    />
                  </g>
                  <defs>
                    <clipPath id="clip0_451_93">
                      <rect width="96" height="96" fill="white" />
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </span>
            <table>
              <thead>
                <tr>
                  <th class="coluna-fixa">Apurações</th>
                  <th
                    class="empresa text-right"
                    *ngFor="let item of dadosTabela?.detalhes"
                  >
                    {{ item.apelidoApuracao }}
                    <span>{{ utils.formatarCnpj(item.cnpj) }}</span>
                  </th>
                  <th class="empresa text-right">Valor Total</th>
                </tr>
              </thead>
              <tbody>
                <ng-container *ngFor="let item of dadosTabela?.dados">
                  <tr
                    [ngClass]="{
                      'registro-ativo':
                        item.filhos?.length > 0 && item.expandido
                    }"
                  >
                    <td
                      class="coluna-fixa"
                      (click)="expandirRegistro(item)"
                      [ngClass]="{ teste: item.expandido }"
                    >
                      {{ item.descricaoValor }}
                      <svg
                        *ngIf="item.filhos?.length > 0"
                        [ngClass]="{ ativo: item.expandido }"
                        xmlns="http://www.w3.org/2000/svg"
                        class="svg-icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                      >
                        <path
                          d="M680.1408 414.976c9.9328-8.704 24.2176-6.656 31.8976 4.608a27.8016 27.8016 0 0 1-4.096 35.84l-172.032 149.76a35.6352 35.6352 0 0 1-47.8208 0l-172.032-149.7088a27.8016 27.8016 0 0 1-4.096-35.9424c7.68-11.1616 22.016-13.2096 31.8976-4.608L512 561.3056l168.1408-146.2784z"
                        />
                      </svg>
                    </td>
                    <ng-container *ngFor="let periodo of item.valoresPeriodos">
                      <td>
                        <div class="text-right">
                          {{ getValor(periodo) }}
                        </div>
                      </td>
                    </ng-container>
                    <td
                      class="text-right total"
                      [ngClass]="{
                        'registro-ativo':
                          item.filhos?.length > 0 && item.expandido
                      }"
                    >
                      {{ aplicarMascara(item.total) }}
                    </td>
                  </tr>
                  <ng-container *ngIf="item.expandido">
                    <ng-container *ngFor="let itemNivel2 of item.filhos">
                      <tr
                        [ngClass]="{
                          'sub-registro-ativo':
                            itemNivel2.tipoSubRegistro == 1 &&
                            itemNivel2.expandido,
                          'sub-registro': true
                        }"
                      >
                        <td
                          class="coluna-fixa"
                          (click)="expandirRegistro(itemNivel2)"
                          [ngClass]="{
                            'sub-registro-ativo': itemNivel2.expandido
                          }"
                        >
                          {{ itemNivel2.descricaoValores[0] }}
                          <svg
                            *ngIf="itemNivel2.tipoSubRegistro === 1"
                            [ngClass]="{ ativo: itemNivel2.expandido }"
                            xmlns="http://www.w3.org/2000/svg"
                            class="svg-icon"
                            viewBox="0 0 1024 1024"
                            version="1.1"
                          >
                            <path
                              d="M680.1408 414.976c9.9328-8.704 24.2176-6.656 31.8976 4.608a27.8016 27.8016 0 0 1-4.096 35.84l-172.032 149.76a35.6352 35.6352 0 0 1-47.8208 0l-172.032-149.7088a27.8016 27.8016 0 0 1-4.096-35.9424c7.68-11.1616 22.016-13.2096 31.8976-4.608L512 561.3056l168.1408-146.2784z"
                            />
                          </svg>
                        </td>
                        <ng-container
                          *ngFor="let periodo of itemNivel2.valoresPeriodos"
                        >
                          <td>
                            <div class="text-right">
                              {{ getValor(periodo) }}
                            </div>
                          </td>
                        </ng-container>
                        <td
                          class="text-right total"
                          [ngClass]="{
                            'sub-registro-ativo':
                              itemNivel2.tipoSubRegistro == 1 &&
                              itemNivel2.expandido
                          }"
                        >
                          {{ aplicarMascara(itemNivel2.total) }}
                        </td>
                      </tr>
                      <ng-container *ngIf="itemNivel2.expandido">
                        <ng-container
                          *ngFor="let itemNivel3 of itemNivel2.filhos"
                        >
                          <tr class="subsub-registro">
                            <td
                              class="coluna-fixa"
                              (click)="expandirRegistro(itemNivel3)"
                            >
                              {{ itemNivel3.descricaoValores[0] }}
                            </td>
                            <ng-container
                              *ngFor="let periodo of itemNivel3.valoresPeriodos"
                            >
                              <td>
                                <div class="text-right">
                                  {{ getValor(periodo) }}
                                </div>
                              </td>
                            </ng-container>
                            <td class="text-right total">
                              {{ aplicarMascara(itemNivel3.total) }}
                            </td>
                          </tr>
                        </ng-container>
                      </ng-container>
                    </ng-container>
                  </ng-container>
                </ng-container>
              </tbody>
            </table>
          </div>
        </div>
        <div
          *ngIf="grupoSelecionado && dadosTabela?.detalhes.length <= 0"
          class="card card-info p-0 mt-3"
        >
          <div class="topo-tabela py-3 text-left">
            <div class="pl-3 conteudo-info">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24px"
                height="24px"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M9 6L15 12L9 18"
                  stroke="#888"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
              <p class="m-0">
                Este grupo só possui apurações do tipo
                <b>{{ retornarFormasApuracao(dadosTabela.disponiveis) }}.</b>
              </p>
            </div>
          </div>
        </div>
        <div *ngIf="!grupoSelecionado" class="card card-info p-0 mt-3">
          <div class="topo-tabela py-3 text-left">
            <div class="pl-3 conteudo-info">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24px"
                height="24px"
                viewBox="0 0 24 24"
                fill="none"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="#888"
                  stroke-width="1.5"
                />
                <path
                  d="M12 17V11"
                  stroke="#888"
                  stroke-width="1.5"
                  stroke-linecap="round"
                />
                <circle
                  cx="1"
                  cy="1"
                  r="1"
                  transform="matrix(1 0 0 -1 11 9)"
                  fill="#888"
                />
              </svg>
              <p class="m-0">
                Nenhum grupo foi criado ainda. Por favor, adicione um novo grupo
                para começar.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
