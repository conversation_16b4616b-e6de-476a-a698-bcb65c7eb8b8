.atividades-texto {
  font-size: 0.9rem;
}

.card-laranja {
  background-color: #000646;
  color: white;
}

.card-cinza {
  background-color: #4d4d4f;
  color: white;
}

.card-claro {
  /* background-color: white; */
  color: #4d4d4f;
}

.dimensoes-relatorios-padrao {
  padding: 3rem 3rem 0rem 3rem;
}

.margem-top-padrao {
  margin-top: 3rem !important;
}

.preview-card {
  max-height: 85%;
  margin-top: 1.4rem;
  /* max-width: 95%; */
}

.periodo-card {
  border: solid;
  border-color: rgba(252, 144, 3, 0.863);
  color: rgba(252, 144, 3, 0.842);
  text-align: center;
  max-width: 5rem;
  height: 5rem;
}

.borda-periodo-label {
  padding-top: 1.4rem;
}

.droplist {
  border-radius: 0.3rem;
  max-height: 13rem;
  background-color: rgba(255, 255, 255, 0.675);
}

.cursorPointer {
  cursor: pointer;
}

.align-circle {
  text-align: -webkit-center;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.cor-valor {
  color: #4d4d4f;
}

.margin-inputs {
  margin-left: 0.8rem;
}

.droplist-text {
  font-size: 0.9rem;
}

.texto-descricao-limitada {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.switch-tela-principal {
  font-size: 20px;
}

.form-check-label {
  margin-top: 0.25rem !important;
}

.input-filtro-apuracoes {
  border: 0;
  outline: 0;
  border-bottom: 1px solid #6c757d7a;
}

.linha-apuracao-selecionada {
  background-color: #f8d4c0;
}

.linha-apuracao {
  cursor: pointer;
}

.linha-apuracao:hover {
  background-color: #f8d4c0;
}

.select-border {
  border: unset;
}

.coluna-nomes {
  min-width: 5rem !important;
  cursor: pointer;
}

.card-body {
  height: 50vh;
  overflow-y: auto;
}

body {
  line-height: 1.5;
}

.content {
  box-sizing: border-box;
  max-width: 800px;
  width: 100%;
  padding: 0 20px;
  margin: 0 auto;
}

h1 {
  font-family: Lobster, Georgia, serif;
  font-size: 5rem;
  color: lightblue;
  text-align: center;
  border-bottom: 5px dotted lightblue;
}

#timeline,
.timeline-item,
.timeline-content,
.timeline-dot {
  margin: 0;
  padding: 0;
}

.timeline-date {
  width: 83%;
  text-align: right;
}
.timeline-item {
  display: grid;
  grid-template-columns: 1fr;
  padding: 20px 0 20px 38px;
  position: relative;

  &:before {
    content: "";
    position: absolute;
    top: 0;
    left: 10px;
    bottom: 0;
    width: 2px;
    background-color: #e5e5e5;
  }

  @media screen and (min-width: 600px) {
    grid-template-columns: 1fr 4fr;
    grid-gap: 30px 51px;
    padding-left: 0;

    &:before {
      left: calc(60% / 3 + 8px);
      transform: translateX(-50%);
    }
  }

  .timeline-time {
    padding-top: 2px;
    color: #999;
    font-size: 0.9rem;
    width: 83%;
    text-align: right !important;
    @media screen and (min-width: 600px) {
      text-align: right;
    }
  }

  .timeline-user {
    padding-top: 2px;
    color: #999;
    font-size: 0.9rem;
  }

  .timeline-dot {
    position: relative;

    &:not(:last-child) {
      margin-bottom: 30px;
    }

    &:before {
      content: "";
      position: absolute;
      left: -53px;
      width: 40px;
      height: 40px;
      background-color: #e5e5e5;
      border: 5px solid #f0f0f0;
      border-radius: 50%;
    }

    @media screen and (min-width: 600px) {
      &:not(:last-child) {
        margin-bottom: 0;
      }

      &:before {
        top: 0;
      }
    }
  }
}

.nome-diretorio {
  font-size: 0.9rem;
}
