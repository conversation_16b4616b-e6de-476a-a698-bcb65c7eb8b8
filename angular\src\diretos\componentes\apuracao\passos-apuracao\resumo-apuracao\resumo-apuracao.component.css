section {
  background-position-y: 2.5rem;
  background-position-x: 2.5rem;
  background-image: url("../../../../../assets/Tax/background-default.png") !important;
  background-size: cover !important;
  background-repeat: no-repeat;
}

.card-padrao {
  font-weight: bold;
  /* border-radius: 1rem; */
  border: 1px solid gray;
  font-weight: 100;
}

.card-padrao > .card-header {
  text-align: center;
  font-size: 15px;
  padding: 0.3rem;
  background-color: #d3d3d4;
}

.card-erro {
  font-weight: bold;
  /* border-radius: 1rem; */
  border: 1px solid #842029ab;
  font-weight: 100;
}

.card-erro > .card-header {
  text-align: center;
  font-size: 15px;
  padding: 0.3rem;
  color: #842029;
  background-color: #f8d7da !important;
  /* border-top-right-radius: 1rem;
  border-top-left-radius: 1rem; */
}

.coluna-direita {
  border-right: 2px solid rgba(0, 0, 0, 0.1);
}

.periodo-inativo {
  color: #0000004f;
  pointer-events: none;
  background: #ececec;
  min-height: 11.5rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  border-color: #000646;
  border-bottom-left-radius: 1rem;
  border-bottom-right-radius: 1rem;
}

.card-resumo-apuracao {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  padding-bottom: 2.2rem;
}

.icone-alerta {
  color: orange;
  font-size: 0.8em;
  position: absolute;
  margin-left: 5px;
  margin-top: 2px;
}

.icone-bloqueio {
  color: #141619;
  position: absolute;
  right: 1rem;
  top: 0.5rem;
  cursor: pointer;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/*ESTILOS DA BARRA DE PROGRESSO COM STEPS*/
/* -------------------------------------------------------------------------
  VERTICAL STEPPERS
-------------------------------------------------------------------------- */
.c-timeline__item {
  position: relative;
  display: flex;
  gap: 1.5rem;
  font-size: 11px;
}

.c-timeline__item:last-child .c-timeline__content:before {
  display: none;
}

.c-timeline__content {
  flex: 1;
  position: relative;
  order: 1;
}

.c-timeline__content:before {
  content: "";
  position: absolute;
  right: -16px;
  top: 0;
  height: 100%;
  width: 1px;
  background-color: lightgrey;
}

.c-timeline__content:after {
  content: "";
  position: absolute;
  right: -23px;
  top: 0;
  width: 15px;
  height: 15px;
  background-color: #fff;
  z-index: 1;
  border: 1px solid lightgrey;
  border-radius: 50%;
}

.status-verde:after {
  background-color: #4caf50;
  border-color: #4caf50;
}

.status-azul:after {
  background-color: #1976d2;
  border-color: #1976d2;
}

.status-vermelho:after {
  background-color: #f44336;
  border-color: #f44336;
}

.c-timeline__title {
  font-weight: bold;
  margin-bottom: 0.2rem;
  text-align: end;
}

.c-timeline__desc {
  color: grey;
  text-align: end;
}

tr {
  cursor: context-menu !important;
}
