import { CurrencyPipe } from "@angular/common";
import { Component, Injector, OnInit } from "@angular/core";
import { Apuracao } from "@diretos/entidades/apuracao";
import { ValoresGraficoImpostoRecolher } from "@diretos/entidades/dashboard";
import { EnumFormaApuracao } from "@diretos/enums/enum-forma-aputacao";
import { DashboardServer } from "@diretos/servicos/dashboard-server";
import { Utils } from "@diretos/utils/utils";
import { AppComponentBase } from "@shared/app-component-base";
import { Chart, registerables } from "chart.js";

@Component({
  selector: "app-dashboard-imposto-a-recolher",
  templateUrl: "./dashboard-imposto-a-recolher.component.html",
  styleUrls: ["./dashboard-imposto-a-recolher.component.css"],
})
export class DashboardImpostoARecolherComponent
  extends AppComponentBase
  implements OnInit
{
  _apuracaoId: number;
  _dadosCardImpostoRecolher: ValoresGraficoImpostoRecolher[];
  _dadosApuracao: Apuracao;
  _periodoInicio: number;
  _periodoFim: number;
  _tipoCalculo: number;
  _tituloModal: string;
  utils = new Utils();

  constructor(
    injector: Injector,
    private _currency: CurrencyPipe,
    private _dashboardServer: DashboardServer
  ) {
    super(injector);
  }

  async ngOnInit(): Promise<void> {
    Chart.register(...registerables);
    this.getDadosGraficoImpostoRecolher(this._apuracaoId);
  }

  getDadosGraficoImpostoRecolher(apuracaId: number) {
    this._dashboardServer
      .getDadosGraficoImpostoRecolher(
        apuracaId,
        this._periodoInicio,
        this._periodoFim,
        this._tipoCalculo
      )
      .toPromise()
      .then((result) => {
        this._dadosCardImpostoRecolher = result;
        this.formatarDadosCardImpostoRecolher();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      });
  }

  formatarDadosCardImpostoRecolher() {
    const listaValorImpostoApurado = [];
    const listaValorImpostoRecolhido = [];
    const labels = [];

    this._dadosCardImpostoRecolher
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((imposto) => {
        listaValorImpostoApurado.push(imposto.valorImpostoApurado);
        listaValorImpostoRecolhido.push(imposto.valorImpostoRecolhido);
        if (imposto.ajusteAnual == true) labels.push("Ajuste anual");
        else
          labels.push(
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(imposto.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  imposto.ordem.toString()
                )
          );
      });

    this.criarRelatorioImpostoARecolher(
      listaValorImpostoApurado,
      listaValorImpostoRecolhido,
      labels
    );
  }

  criarRelatorioImpostoARecolher(
    listaValorImpostoApurado,
    listaValorImpostoRecolhido,
    labels
  ) {
    const currencyPipe = this._currency;
    // const dataImposto = [];
    // const listaCoresColunasGrafico = [];
    // for (let i = 0; i < listaValorImpostoApurado.length; i++) {
    //   if (i == 0) {
    //     dataImposto.push([0, listaValorImpostoApurado[i].value]);
    //     listaValorImpostoApurado[i].value < 0 ? listaCoresColunasGrafico.push('#f3d2be') : listaCoresColunasGrafico.push('#000646');
    //   }
    //   else {
    //     let valorPeriodoAnterior = dataImposto[i - 1][1];
    //     let valorPeriodoAtual = listaValorImpostoApurado[i].value;
    //     dataImposto.push([valorPeriodoAnterior, valorPeriodoAtual]);
    //     valorPeriodoAtual < 0 ? listaCoresColunasGrafico.push('#f3d2be') : listaCoresColunasGrafico.push('#000646');
    //   }
    // };

    new Chart("RelatorioImpostoARecolher", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            label: "Recolhido",
            data: listaValorImpostoRecolhido,
            backgroundColor: "#000646",
            barPercentage: 0.5,
            type: "bar",
            order: 2,
          },
          {
            label: "Apurado",
            data: listaValorImpostoApurado,
            backgroundColor: "grey",
            type: "line",
            order: 1,
          },
        ],
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "top",
          },
          tooltip: {
            yAlign: "bottom",
            callbacks: {
              label: function (tooltipItem) {
                try {
                  return `Recolhido ${currencyPipe.transform(
                    tooltipItem.chart.data.datasets[0].data[
                      tooltipItem.dataIndex
                    ].toString(),
                    " ",
                    "symbol"
                  )} | Apurado ${currencyPipe.transform(
                    tooltipItem.chart.data.datasets[1].data[
                      tooltipItem.dataIndex
                    ].toString(),
                    " ",
                    "symbol"
                  )}`;
                } catch (e) {
                  return "";
                }
              },
            },
          },
          title: {
            display: false,
          },
        },
      },
    });
  }

  customizeLabel(args) {
    return `${(args.percent * 100).toFixed(2)}%`;
  }

  customizeTooltip = (args: any) => ({
    text: `${args.argumentText}: ${this._currency.transform(args.valueText)}`,
  });
}

export class ColunasRelatorio {
  descricao: string;
  valor: any;
  color: string;
}
