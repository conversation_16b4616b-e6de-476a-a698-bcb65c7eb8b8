.selecionado {
  color: #000646;
  background-color: rgba(0, 6, 70, 0.08) !important;
  border-radius: 6px;
  font-weight: 500;
}

.margem-lateral-submenu {
  padding-left: 2rem;
}

.nav-icon {
  font-size: 0.9rem !important;
}

.nav-sidebar .nav-link > .right:nth-child(2),
.nav-sidebar .nav-link > p > .right:nth-child(2) {
  right: 1rem !important;
}

/* Melhor<PERSON> no design da sidebar */
.nav-link {
  border-radius: 6px !important;
  margin: 2px 0 !important;
  padding: 8px 16px !important;
  transition: all 0.2s ease !important;
}

.nav-link:hover {
  background-color: rgba(0, 6, 70, 0.05) !important;
  transform: translateX(2px);
}

.nav-item hr {
  border-color: rgba(0, 0, 0, 0.1) !important;
  margin: 8px 0 !important;
}

.nav-item small {
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.6) !important;
  font-size: 0.75rem !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
}

/* Remove a borda lateral dos itens do submenu */
.nav-flat.nav-sidebar > .nav-item .nav-treeview .nav-item > .nav-link,
.nav-flat.nav-sidebar > .nav-item > .nav-treeview .nav-item > .nav-link {
  border-left: none !important;
  margin: 2px 0 !important;
  padding: 8px 16px 8px 2.5rem !important;
}

/* Adiciona espaçamento lateral na sidebar */
.nav-sidebar {
  padding: 0 8px !important;
}
