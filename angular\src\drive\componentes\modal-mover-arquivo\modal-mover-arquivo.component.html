<div
  *ngIf="_carregando"
  class="freeze-ui"
  data-text=" "
  style="position: absolute"
></div>
<div class="modal-header" style="display: flow">
  <div class="col-12 d-flex p-0">
    <h4 class="modal-title" data-toggle="tooltip" [title]="_tituloModal">
      {{ _tituloModal }}
    </h4>
    <button
      type="button"
      class="close"
      aria-label="Close"
      (click)="bsModalRef.hide()"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <small>Selecione o diretório para armazenar os arquivos</small>
</div>

<form>
  <input
    class="form-control form-control-sm col-6 ml-3 mt-3"
    type="text"
    id="nomeDiretorio"
    [ngModelOptions]="{ standalone: true }"
    [(ngModel)]="_filtroListaDiretorio"
    placeholder="Pesquisa..."
  />
</form>

<div class="tree-view mt-2" id="tree-view">
  <div *ngFor="let node of retornarListaDiretoriosFiltrados()">
    <div
      (click)="toggleNode(node)"
      (mouseover)="onMouseOver($event, node)"
      (mouseleave)="onMouseLeave($event)"
      class="folder unselectable"
      [ngClass]="{ 'linha-selecionada': _idDiretorioAtual == node.id }"
    >
      <i class="fa-folder far"></i>
      {{ _utilsDrive.parseCnpj(node.nome) }}
      <span *ngIf="node.razaoSocial"> - {{ node.razaoSocial }} </span>
      <i
        *ngIf="node.carregando"
        class="fa-solid fa-circle-notch fa-spin ml-2"
        style="animation-duration: var(--fa-animation-duration, 2s) !important"
      ></i>
      <i
        *ngIf="!node.carregando"
        class="ml-2"
        [class]="
          node.apresentarSubdiretorio
            ? 'fa fa-chevron-up'
            : 'fa fa-chevron-down'
        "
      ></i>
      <i
        class="fas fa-folder-plus fa-lg ml-2"
        style="color: #000646"
        *ngIf="apresentarBotaoNovoDiretorio(node)"
        (click)="
          node.apresentarFormNovoSubdiretorio = true;
          toggleNode(node);
          $event.stopPropagation()
        "
      ></i>
      <div
        *ngIf="
          node.apresentarFormNovoSubdiretorio && _idDiretorioAtual == node.id
        "
      >
        <ng-container
          *ngTemplateOutlet="formSubDiretorio; context: { $implicit: node }"
        ></ng-container>
      </div>
    </div>
    <div class="parent-ul" *ngIf="node.apresentarSubdiretorio">
      <ng-container
        *ngTemplateOutlet="
          treeNode;
          context: { $implicit: node.listaSubdiretorios }
        "
      ></ng-container>
    </div>
  </div>
</div>

<ng-template #treeNode let-nodes>
  <div *ngFor="let node of nodes">
    <div
      (click)="toggleNode(node)"
      class="folder unselectable"
      [ngClass]="{ 'linha-selecionada': _idDiretorioAtual == node.id }"
      (mouseover)="onMouseOver($event, node)"
      (mouseleave)="onMouseLeave($event)"
    >
      <i *ngIf="!node.type" class="fa-folder far"></i>
      {{ node.nome }}
      <i
        *ngIf="node.carregando"
        class="fa-solid fa-circle-notch fa-spin ml-2"
        style="animation-duration: var(--fa-animation-duration, 2s) !important"
      ></i>
      <i
        class="ml-2"
        *ngIf="node.listaSubdiretorios?.length !== 0 && !node.carregando"
        [class]="
          node.apresentarSubdiretorio
            ? 'fa fa-chevron-up'
            : 'fa fa-chevron-down'
        "
      ></i>
      <i
        class="add-folder-icon fas fa-folder-plus fa-lg ml-2"
        style="color: #000646"
        *ngIf="apresentarBotaoNovoDiretorio(node)"
        (click)="
          node.apresentarFormNovoSubdiretorio = true;
          toggleNode(node);
          $event.stopPropagation()
        "
      ></i>
      <div
        *ngIf="
          node.apresentarFormNovoSubdiretorio && _idDiretorioAtual == node.id
        "
      >
        <ng-container
          *ngTemplateOutlet="formSubDiretorio; context: { $implicit: node }"
        ></ng-container>
      </div>
    </div>
    <div
      class="children-ul"
      *ngIf="node.listaSubdiretorios && node.apresentarSubdiretorio"
    >
      <ng-container
        *ngTemplateOutlet="
          treeNode;
          context: { $implicit: node.listaSubdiretorios }
        "
      ></ng-container>
    </div>
  </div>
</ng-template>

<ng-template #formSubDiretorio let-node>
  <form id="form-group" [formGroup]="diretorioForm">
    <div class="input-group pl-2 pr-2">
      <input
        class="form-control form-control-sm"
        type="text"
        id="nomeDiretorio"
        required
        formControlName="nome"
        placeholder="Nome"
        style="background-color: white !important"
        (click)="$event.stopPropagation()"
      />
      <button
        type="submit"
        class="input-group-text btn-primary"
        (click)="aoAdicionarDiretorio(node); $event.stopPropagation()"
        [disabled]="this.diretorioForm.get('nome').value === null"
      >
        <i class="fa fa-plus"></i>
      </button>
    </div>
  </form>
</ng-template>

<abp-modal-footer
  [cancelDisabled]="_carregando"
  [saveLabel]="_labelBotaoSalvar"
  [saveDisabled]="_carregando || !validarDiretorioSelecionado()"
  (onSaveClick)="moverDiretorio()"
  (onCancelClick)="bsModalRef.hide()"
></abp-modal-footer>
