@media (min-width: 576px) {
  .nav-user-menu .user-image {
    float: none;
    line-height: 10px;
    margin-top: -8px;
  }
}

@font-face {
  font-family: "GoodTimes";
  src: local("GoodTimes"), url(../assets/good-times-rg.ttf) format("truetype");
}

@font-face {
  font-family: "NunitoSans";
  src: local("GoodTimes"),
    url(../assets/fonts/NunitoSans-Light.ttf) format("truetype");
}

.swal2-html-container {
  padding: 0 !important;
}

.swal2-popup.swal2-toast.swal2-show {
  padding: 0.5rem !important;
}

.nav-user-menu .user-image {
  border-radius: 50%;
  float: left;
  height: 1.4rem;
  margin-top: -4px;
  width: 1.4rem;
}

.form-group.required .col-form-label:after {
  color: #d00;
  content: "*";
  position: absolute;
  margin-left: 3px;
}

.icone-excluir {
  color: red;
  margin-left: 1rem;
}

.icone-inativo {
  color: red;
}

.icone-ativo {
  color: green;
}

.icone-editar {
  color: #4d4d4f;
}

.icone-acao {
  color: #000646;
}

.container-fluid {
  padding-top: 1rem;
}

.botao-pesquisar-filtro {
  margin-left: 0.3rem;
}

.obrigatorio:after {
  color: #d00;
  content: "*";
  font-family: Glyphicons Halflings;
}

label {
  font-weight: 500 !important;
  font-size: 15px;
}

.fa-pencil-alt {
  cursor: pointer;
  color: #4d4d4f;
}

.fa-trash-alt {
  cursor: pointer;
  color: red !important;
}

.fa-hand-pointer {
  cursor: pointer;
}

button {
  cursor: pointer !important;
}

.button:houver {
  background-color: #051627 !important;
}

.campoComMascara {
  border-style: hidden;
}

input:disabled {
  background-color: unset;
  border-color: unset;
}

a {
  cursor: pointer;
}

.login-page {
  background-color: #fff;
}

.nav-tabs .nav-item.show .nav-link,
.nav-tabs .nav-link.active {
  color: #fff;
  background-color: #000646 !important;
}

.nav-link {
  color: #000646;
}

.color-primary-boost {
  color: #ff5f00;
}

.nav-link:not(.active):hover {
  color: gray !important;
  opacity: 0.5;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: #fff;
  background-color: #000646 !important;
}

.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle {
  color: white !important;
  background-color: #000646 !important;
  border-color: #000646 !important;
}

.btn-outline-primary:hover:not(.active) {
  color: #fff !important;
  background-color: #000646 !important;
  border-color: #000646 !important;
}

.btn-outline-primary {
  color: #000646;
  background-color: #fff;
  border-color: #000646;
}

.btn-primary:focus {
  filter: brightness(88%) !important;
}

.btn-padrao {
  background-color: #000646;
  color: white;
}

.btn-primary {
  color: #fff !important;
  background-color: #000646 !important;
  border-color: #000646 !important;
}

.btn-outline-secondary {
  border-color: #000646 !important;
  color: #000646;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:active,
.btn-outline-secondary:visited {
  background-color: #000646 !important;
}

.fas.fa-search {
  color: white !important;
}

.page-item.active .page-link {
  z-index: 3;
  color: black !important;
  background-color: #f8d4c0;
  border-color: #f8d4c0;
}

.page-link {
  position: relative;
  display: block;
  padding: 0.5rem 0.75rem;
  margin-left: -1px;
  line-height: 1.25;
  color: #74799c;
  background-color: #fff;
  border: 1px solid #dee2e6;
}

a {
  color: #000646;
  text-decoration: none;
  background-color: transparent;
}

.border-primary {
  border-color: #000646 !important;
}

.border-primary-boost {
  border-color: #ff5f00 !important;
}

.item-disabled {
  cursor: no-drop;
}

.mensagem-senha-expirada {
  color: #dc3545;
  font-size: 16px;
}

input[type="checkbox"] {
  cursor: pointer !important;
}

input[type="radio"] {
  cursor: pointer !important;
}

.coluna-descricao-limitada {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.table.table-sm .coluna-tabela-descricao-limitada {
  position: relative;
}

.table.table-sm .coluna-tabela-descricao-limitada span {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: absolute;
  width: 90%;
}

.coluna-tabela-descricao-limitada:before {
  content: "";
  display: inline-block;
}

.freeze-ui:before {
  left: calc(50% - 38px) !important;
  top: calc(50% - 38px) !important;
  border-color: transparent #000646 #000646 !important;
}

.card-header {
  padding: 0.3rem;
}

.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: 1.5rem !important;
  padding-left: 5px;
  text-overflow: ellipsis;
}

.nav-tabs .nav-item {
  margin-bottom: -1px;
  height: 2.2rem;
}

.card-footer {
  padding: 0.4rem;
}

.dropdown-item.active,
.dropdown-item:active {
  color: white !important;
  text-decoration: none;
  background-color: #000646;
}

.dropdown-item.item-submenu {
  font-size: 14px;
}

.sidebar {
  overflow: hidden;
}

.sort-default {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: 0.2rem;
}

.sort-asc {
  position: absolute;
  margin-top: 0.5rem;
  margin-left: 0.2rem;
}

.sort-desc {
  position: absolute;
  margin-left: 0.2rem;
}

.th-sort {
  cursor: pointer;
  user-select: none;
}

.dropdown-toggle.dropdown::before {
  margin-right: 0;
  transform: rotate(-90deg);
}

.dropdown-toggle-split::before {
  transform: rotate(-90deg) !important;
}

th {
  border-color: #dee2e6 !important;
}

.modal-body {
  padding: 1.5rem;
}

.primeiro-passo-ecf {
  min-width: 12rem !important;
}

.botao-mes-ativo {
  background-color: #f3d2be !important;
  color: white !important;
  border-color: #f3d2be !important;
}

.botao-mes-selecionado {
  background-color: #000646 !important;
  color: white;
  border-color: #f3d2be;
}

.botao-mes-inativo {
  background-color: #ccc !important;
  color: white;
  border-color: white;
}

.th-sort {
  cursor: pointer;
  user-select: none;
}

tr {
  cursor: pointer;
}

.icone-abrir-pesquisar-conta-pai {
  background-color: #000646;
}

.progress-bar {
  background-color: #4caf50 !important;
}

.passo-wizard {
  background-color: #f3d2be;
  color: white;
  display: block;
  padding: 0.5rem 1rem;
  cursor: pointer;
  position: relative;
  text-align: center;
}

.passo-wizard:hover {
  background-color: #000646;
  color: white;
}

.passo-wizard:hover:after {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid #000646;
  position: absolute;
  top: 50%;
  margin-top: -20px;
  left: 100%;
  z-index: 2;
}

.passo-wizard:after {
  content: " ";
  display: block;
  width: 0;
  height: 0;
  border-top: 20px solid transparent;
  border-bottom: 20px solid transparent;
  border-left: 20px solid #f3d2be;
  position: absolute;
  top: 50%;
  margin-top: -20px;
  left: 100%;
  z-index: 2;
}

.passo-wizard.selecionado {
  background-color: #000646;
  color: white;
}

.passo-wizard.selecionado:after {
  border-left: 20px solid #000646 !important;
}

.passo-padding-lateral {
  padding-left: 30px;
}

.passo-tamanho-padrao {
  min-width: 12rem;
}

.passo-tamanho-reduzido {
  min-width: 8rem;
}

.passo-calculo {
  background-color: #28a745;
  opacity: 0.5 !important;
}

.passo-calculo:hover {
  background-color: #28a745 !important;
  opacity: 1 !important;
  color: white !important;
}

.passo-calculo:hover:after {
  border-left: 20px solid #28a745 !important;
}

.passo-calculo:after {
  border-left: 20px solid #28a745;
}

.passo-calculo.selecionado {
  background-color: #28a745 !important;
}

.passo-calculo.selecionado:after {
  border-left: 20px solid #28a745 !important;
}

//Integradores
.seta-linha-dados {
  position: absolute;
  left: -1rem;
  margin-top: 2px;
}

.trecho-destacado {
  background-color: yellow !important;
  font-family: inherit !important;
}

ul.nav a:hover {
  color: #d6d6d6 !important;
}

.swal2-title {
  font-size: 20px !important;
  font-weight: 100 !important;
}

.swal2-warning {
  font-size: 1rem !important;
}

.icone-pesquisa-registro-associado {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.badge-primary {
  background-color: #000646 !important;
  font-weight: 100;
}

.botao-sugerir-senha {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.tag-plano-free {
  color: #084298;
  background-color: #cfe2ff;
  border-color: #b6d4fe;
}

.cinza-padrao {
  color: #696c83;
}

.nav-flat {
  margin: 0 !important;
}

.sidebar-mini.sidebar-collapse
  .main-sidebar:not(.sidebar-no-expand).sidebar-focused,
.sidebar-mini.sidebar-collapse .main-sidebar:not(.sidebar-no-expand):hover {
  width: 4.6rem !important;
}

.main-sidebar {
  position: fixed !important;
  overflow-y: auto !important;
  overflow-x: hidden !important;
  height: 100vh !important;
}

td,
th,
option,
label,
span,
button,
b,
.item-submenu,
h2,
p {
  font-family: "Roboto", sans-serif;
}

.mep {
  margin-left: 1rem;
}

.table {
  margin-bottom: 0 !important;
}

.dropdown-toggle {
  background-color: #fa7436 !important;
}

.icone-header {
  font-size: 18px;
  margin-right: 5px;
}

.swal2-info {
  display: none !important;
}

td .campoComMascara {
  text-align: end;
}

.imagem-fundo-plataforma {
  background-image: url("../assets/Tax/background-default.png") !important;
  background-size: cover !important;
  background-repeat: no-repeat;
  background-position-y: 20vh;
}

.imagem-fundo-dashboard {
  background-image: url("../assets/Tax/background-default.png") !important;
  background-size: cover !important;
  background-repeat: no-repeat;
}

.imagem-fundo-banner-apresentacao-modulo {
  background-image: url("../assets/Tax/background-default.png") !important;
  background-size: cover !important;
  background-repeat: no-repeat;
}

.btn.dropdown-toggle.dropdown {
  background-color: white !important;
  border-color: #000646;
  color: #000646;
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.descricao-limitada {
  max-width: 1ch;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.texto-apoio-input {
  font-size: 12px;
}

.icone-collapse {
  padding: 0 6px 0 6px;
}

.ts-hr {
  width: 100%;
  height: 0;
}

.texto-apoio-input {
  font-size: 12px;
}

.coluna-botao-cabecalho {
  justify-content: center;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.coluna-direita-botao-cabecalho {
  border-right: 2px solid rgba(0, 0, 0, 0.1);
}

.wrapper .content-wrapper {
  min-height: 100vh;
}

.swal2-confirm {
  color: #0f5132 !important;
  background-color: #d1e7dd !important;
  border: 1px solid #badbcc !important;
}

.swal2-confirm:focus,
.swal2-confirm:active {
  box-shadow: 0px 5px 10px 0px #0f5132a3 !important;
}

.swal2-deny {
  color: #842029 !important;
  background-color: #f8d7da !important;
  border: 1px solid #f5c2c7 !important;
}

.swal2-deny:focus,
.swal2-deny:active {
  box-shadow: 0px 5px 10px 0px #842028ac !important;
}

.label-descricao-limitada {
  max-width: 98%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ng-dropdown-panel {
  opacity: 1 !important;
  background: white !important;
  border: solid black 1px !important;
}

.center-content {
  height: calc(100% - 58px);
  align-content: center;
}

.custome-blur {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background-color: #000000;
  opacity: 0.5;
  transition: opacity 0.25s;
}

.w-30 {
  width: 30% !important;
}
