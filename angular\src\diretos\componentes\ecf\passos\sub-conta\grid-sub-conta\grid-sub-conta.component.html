<form
  class="form-horizontal"
  autocomplete="off"
  #createUserModal="ngForm"
  (keyup.enter)="getDataPage(1)"
  [formGroup]="dadosGridForm"
>
  <div class="form-row">
    <input
      type="text"
      class="form-control col-md-2 ml-1"
      name="keywordContaContabil"
      id="keywordContaContabil"
      maxlength="255"
      formControlName="keywordContaContabil"
      placeholder="Conta contábil"
    />
    <input
      type="text"
      class="form-control col-md-2 mep"
      name="keywordSubConta"
      id="keywordSubConta"
      maxlength="255"
      formControlName="keywordSubConta"
      placeholder="Subconta"
    />
    <select
      class="form-select col-md-2 mep"
      formControlName="grupoSubContaId"
      id="grupoSubContaId"
    >
      <option [ngValue]="null" selected="selected">Grupo da subconta</option>
      <option
        *ngFor="let linha of listaGrupoSubconta"
        [value]="linha.id"
        data-toggle="tooltip"
        [title]="linha.codigo"
      >
        {{ linha.codigo }}
      </option>
    </select>
    <select
      class="form-select col-md-2 mep"
      formControlName="cargaNaturezaSubContaId"
      id="cargaNaturezaSubContaId"
    >
      <option [ngValue]="null" selected="selected">Natureza da subconta</option>
      <option
        *ngFor="let linha of listaCargaNaturezaSubConta"
        [value]="linha.id"
        data-toggle="tooltip"
        [title]="linha.descricao"
      >
        {{ linha.codigo }} - {{ linha.descricao | slice : 0 : 50 }}
      </option>
    </select>
    <button
      type="button"
      class="btn mep btn-outline-secondary"
      (click)="limparFiltros()"
      data-toggle="tooltip"
      title="Limpar Filtros"
    >
      LIMPAR
    </button>
    <button
      type="button"
      class="btn mep btn-primary"
      (click)="getDataPage(1)"
      data-toggle="tooltip"
      title="Pesquisar"
    >
      FILTRAR
    </button>
    <button
      type="button"
      class="btn mep btn-primary"
      (click)="parametrizarSubConta()"
    >
      <i class="fa fa-plus mr-2"></i>
      PARAMETRIZAR
    </button>
  </div>
</form>
<table class="table table-hover text-nowrap mt-3" [busy]="isTableLoading">
  <thead>
    <tr>
      <th style="width: 2%"></th>
      <th style="width: 50%">Conta contábil</th>
      <th style="width: 30%">Grupo subconta</th>
      <th style="width: 20%" class="text-right"></th>
    </tr>
  </thead>
  <tbody *ngIf="dadosGrid.length > 0">
    <ng-container
      *ngFor="
        let subconta of dadosGrid
          | paginate
            : {
                id: 'gridSubConta',
                itemsPerPage: pageSize,
                currentPage: pageNumber,
                totalItems: totalItems
              };
        let idRegistro = index
      "
    >
      <tr>
        <td
          data-toggle="collapsed"
          attr.data-target="#collapse_{{ idRegistro }}"
          aria-expanded="false"
          (click)="subconta.isCollapsed = !subconta.isCollapsed"
          aria-controls="collapse_{{ idRegistro }}"
          class="collapsed coluna-controle-detalhes"
        >
          <i
            class="fa fa-angle-down"
            aria-hidden="true"
            *ngIf="subconta.isCollapsed"
          ></i>
          <i
            class="fa fa-angle-up"
            aria-hidden="true"
            *ngIf="!subconta.isCollapsed"
          ></i>
        </td>
        <td
          (click)="subconta.isCollapsed = !subconta.isCollapsed"
          class="coluna-tabela-descricao-limitada"
        >
          <span data-toggle="tooltip" [title]="subconta.descricaoContaContabil">
            {{ subconta.codigoContaContabil }} -
            {{ subconta.descricaoContaContabil }}</span
          >
        </td>
        <td>
          {{ subconta.grupoSubConta }}
          <i
            class="fa fa-pencil-alt icone-editar ml-2"
            (click)="
              abrirModalPesquisaGrupoSubconta(subconta); $event.preventDefault()
            "
          ></i>
        </td>
        <td class="text-right">
          <div class="col-1 float-right mr-4">
            <div dropdown>
              <a
                href="javascript:;"
                class="nav-link pt-0 pb-0"
                dropdownToggle
                data-boundary="viewport"
              >
                <i class="fas fa-ellipsis-v icone-dropdown"></i>
              </a>
              <div class="dropdown-menu dropdown-menu-right" *dropdownMenu>
                <span
                  class="dropdown-item"
                  (click)="parametrizarSubContaComValoresDefault(subconta)"
                >
                  Parametrizar para esta subconta
                  <i class="fa fa-plus mr-1 ml-3" style="color: #000646"></i
                ></span>
                <span class="dropdown-item" (click)="delete(subconta)">
                  Excluir todos os vínculos
                  <i class="far fa-trash-alt icone-excluir mr-1"></i
                ></span>
                <span
                  *ngIf="!subconta.isCollapsed"
                  class="dropdown-item"
                  (click)="deleteSubContasFiltradas()"
                >
                  Excluir as subcontas filtradas
                  <i class="far fa-trash-alt icone-excluir mr-1"></i
                ></span>
              </div>
            </div>
          </div>
        </td>
      </tr>
      <tr>
        <td
          colspan="4"
          *ngIf="!subconta.isCollapsed"
          class="pl-3 pt-2 pb-2 pr-3 coluna-collapse"
        >
          <div
            id="collapse_{{ idRegistro }}"
            [ngbCollapse]="subconta.isCollapsed"
          >
            <app-grid-sub-conta-vinculos
              [apuracaoId]="apuracao?.id"
              [contaContabilId]="subconta.idContaContabil"
              [eventoExcluirVinculos]="
                eventoClickExcluirVinculosFiltrados.asObservable()
              "
            >
            </app-grid-sub-conta-vinculos>
          </div>
        </td>
      </tr>
    </ng-container>
  </tbody>

  <tbody *ngIf="dadosGrid.length == 0" class="text-center">
    <td colspan="4" style="padding: 1rem"><b>Não há registros</b></td>
  </tbody>
</table>
<div class="card-footer bg-light border-top">
  <div class="row">
    <div class="col-sm-4 col-12 text-sm-left text-center"></div>
    <div class="col-sm-4 col-12 text-center">
      <p class="mb-0 my-2">
        {{ "TotalRecordsCount" | localize : totalItems }}
      </p>
    </div>
    <div class="col-sm-4 col-12">
      <div class="float-sm-right m-auto">
        <abp-pagination-controls
          id="gridSubConta"
          (pageChange)="getDataPage($event)"
        >
        </abp-pagination-controls>
      </div>
    </div>
  </div>
</div>
