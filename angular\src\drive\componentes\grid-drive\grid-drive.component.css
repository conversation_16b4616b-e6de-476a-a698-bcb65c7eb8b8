.icone-excluir {
  color: #f44336;
  margin-left: 8px;
}

.coluna-acoes {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
}

.opcoes {
  list-style: none;
  margin: 0;
  padding: 0;
  z-index: 10;
  background-color: white;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
}

.opcoes :hover {
  background-color: white;
}

.opcoesAtivas {
  visibility: inherit;
}

.opcoesInativas {
  visibility: hidden;
  display: table-column;
  position: fixed;
  top: 309px;
  left: 1106px;
}

.card {
  width: 100%;
  z-index: 10;
}

.progress {
  border-radius: 5px;
  animation-duration: 0.1s !important;
}

.bg-info {
  background-color: #ebe5e2 !important;
  color: black !important;
}

.secao-inclucao-arquivo {
  border: dashed 2px #80808054;
  border-radius: 10px;
  margin-top: 2rem;
}

.nav-link {
  color: black !important;
}

.linha-selecionada {
  background-color: #cfe2ff !important;
}

.text-truncate {
  max-width: 50ch;
}

.row {
  margin-left: 0;
  margin-right: 0;
}

.fit-content {
  max-width: fit-content !important;
  max-height: fit-content !important;
  align-items: center !important;
  justify-content: space-evenly !important;
  display: flex !important;
}

.truncate {
  max-width: 100% !important;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.select-tag {
  width: 100%;
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.select-tag-area {
  min-width: 250px !important;
}

.form-select {
  outline: none !important;
}

.form-row > div {
  padding-left: 0 !important;
  padding-right: 15px !important;
}

table {
  -webkit-user-select: none; /* Safari */
  -ms-user-select: none; /* IE 10 and IE 11 */
  user-select: none; /* Standard syntax */
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.upload-area {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px dashed darkgray;
  border-radius: 5px;
  margin-top: 0.5rem;
  padding: 1rem;
  height: 18vh;
  margin-bottom: 0.5rem;
}

.aviso-upload {
  color: #000000;
  border-color: #000000;
  border-radius: 5px 5px 0 0;
}

.aviso-upload-body {
  display: flex;
  flex-direction: column;
  padding-top: 5px;
  padding-bottom: 7px;
  gap: 0.2rem;
}

.card {
  user-select: none;
}

@media (max-width: 768px) {
  .select-tag-area {
    min-width: 100% !important;
  }
}

tr {
  cursor: default;
}

i {
  cursor: pointer;
}

.custom-ng-select {
  cursor: pointer !important;
}

.indicador-diretorio-sistema {
  color: #000646;
  margin-left: 5px;
}
