<aside
  class="main-sidebar sidebar-primary"
  style="background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1)"
>
  <div
    class="text-left pt-3 pb-1"
    (click)="manipularClickIconeMenu()"
    style="cursor: pointer; margin-left: 2rem; font-size: 18px"
  >
    <i class="fas fa-bars" style="color: #6c757d"></i>
  </div>

  <nav class="mt-2">
    <ul
      data-widget="treeview"
      role="menu"
      data-accordion="false"
      class="nav nav-pills nav-sidebar flex-column nav-flat"
    >
      <li class="nav-item">
        <a
          data-toggle="tooltip"
          class="nav-link sub-item"
          [class.selecionado]="verificarSeMenuEstaAtivo('/app/plataforma')"
          [href]="tratarAppPathDaRota('/app/plataforma')"
          title="Home"
        >
          <i class="fa-home fas nav-icon"></i>
          <p
            class="label-item-menu"
            [hidden]="!verificarSeMenuEstaAberto()"
            style="display: inline"
          >
            Home
          </p>
        </a>
      </li>

      <li
        class="nav-item mt-2"
        [hidden]="!verificarSeMenuEstaAberto() || vefificarAcessoHost()"
      >
        <small class="pl-4" style="display: inline; opacity: 0.6"
          >Módulos</small
        >
        <hr class="ml-4 mb-0 mt-0" />
      </li>

      <!-- Diretos -->
      <li class="nav-item">
        <a
          data-toggle="tooltip"
          class="nav-link sub-item"
          *ngIf="
            !vefificarAcessoHost() &&
            !verificarSePossuiModuloAtivado(EnumModulo.TsDiretos)
          "
          (click)="abrirBannerApresentacaoModulo(EnumModulo.TsDiretos)"
          title="TS - Diretos"
        >
          <i class="fa-funnel-dollar fas nav-icon"></i>
          <p
            class="label-item-menu"
            [hidden]="!verificarSeMenuEstaAberto()"
            style="display: inline"
          >
            TS - Diretos
          </p>
        </a>
      </li>
      <li
        class="nav-item has-treeview"
        *ngIf="
          !vefificarAcessoHost() &&
          verificarSePossuiModuloAtivado(EnumModulo.TsDiretos)
        "
      >
        <a
          class="nav-link"
          href="javascript:;"
          (click)="collapseSubMenuDiretos = !collapseSubMenuDiretos"
          data-toggle="tooltip"
        >
          <i class="fa-funnel-dollar fas nav-icon"></i>
          <p [hidden]="!verificarSeMenuEstaAberto()">
            <i
              [hidden]="!collapseSubMenuDiretos"
              class="right fas fa-angle-down"
            ></i>
            <i
              [hidden]="!!collapseSubMenuDiretos"
              class="right fas fa-angle-up"
            ></i>
            <span class="label-item-menu" style="display: inline"
              >TS - Diretos</span
            >
            <i
              id="icone-loader-modulo-{{ EnumModulo.TsDiretos }}"
              style="
                animation-duration: var(--fa-animation-duration, 2s) !important;
                display: none !important;
                opacity: 0.8;
              "
              class="fas fa-circle-notch fa-spin ml-2"
            ></i>
          </p>
        </a>
        <ul
          class="nav nav-treeview"
          [collapse]="collapseSubMenuDiretos"
          [isAnimated]="true"
        >
          <li class="nav-item">
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              *ngIf="
                isGranted(PermissoesDiretos.CadastroVisualizador) ||
                isGranted(PermissoesDiretos.CadastroEditor)
              "
              [class.selecionado]="
                verficarSubItemDiretosSelecionado(EnumAbaMenuDiretos.apuracao)
              "
              (click)="
                acessarModulo(EnumModulo.TsDiretos, EnumAbaMenuDiretos.apuracao)
              "
              title="Apurações"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-calculator nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Apurações
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.Dashboard')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDiretosSelecionado(EnumAbaMenuDiretos.dashboard)
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDiretos,
                  EnumAbaMenuDiretos.dashboard
                )
              "
              title="Dashboard"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-chart-line nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Dashboard
              </p>
            </a>
          </li>
          <li class="nav-item" *ngIf="permission.isGranted('PagesDiretos.Ecf')">
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDiretosSelecionado(EnumAbaMenuDiretos.ecf)
              "
              (click)="
                acessarModulo(EnumModulo.TsDiretos, EnumAbaMenuDiretos.ecf)
              "
              title="ECF"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-file-contract nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                ECF
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesDiretos.Repositorio')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDiretosSelecionado(
                  EnumAbaMenuDiretos.repositorio
                )
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDiretos,
                  EnumAbaMenuDiretos.repositorio
                )
              "
              title="Repositórios"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-database nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Repositórios
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="
              permission.isGranted('PagesDiretos.TemplateIntegracaoBalancete')
            "
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDiretosSelecionado(
                  EnumAbaMenuDiretos.integradores
                )
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDiretos,
                  EnumAbaMenuDiretos.integradores
                )
              "
              title="Integradores"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-sitemap nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Integradores
              </p>
            </a>
          </li>
        </ul>
      </li>

      <!-- Drive -->
      <li class="nav-item">
        <a
          data-toggle="tooltip"
          class="nav-link sub-item"
          *ngIf="
            !vefificarAcessoHost() &&
            !verificarSePossuiModuloAtivado(EnumModulo.TsDrive)
          "
          (click)="abrirBannerApresentacaoModulo(EnumModulo.TsDrive)"
          title="TS - Drive"
        >
          <i class="fas fa-cloud nav-icon"></i>
          <p
            class="label-item-menu"
            [hidden]="!verificarSeMenuEstaAberto()"
            style="display: inline"
          >
            TS - Drive
          </p>
        </a>
      </li>
      <li
        class="nav-item has-treeview"
        *ngIf="
          !vefificarAcessoHost() &&
          verificarSePossuiModuloAtivado(EnumModulo.TsDrive)
        "
      >
        <a
          class="nav-link"
          href="javascript:;"
          (click)="collapseSubMenuDrive = !collapseSubMenuDrive"
          data-toggle="tooltip"
        >
          <i class="fas fa-cloud nav-icon"></i>
          <p [hidden]="!verificarSeMenuEstaAberto()">
            <i
              [hidden]="!collapseSubMenuDrive"
              class="right fas fa-angle-down"
            ></i>
            <i
              [hidden]="!!collapseSubMenuDrive"
              class="right fas fa-angle-up"
            ></i>
            <span class="label-item-menu" style="display: inline"
              >TS - Drive</span
            >
            <i
              id="icone-loader-modulo-{{ EnumModulo.TsDrive }}"
              style="
                animation-duration: var(--fa-animation-duration, 2s) !important;
                display: none !important;
                opacity: 0.8;
              "
              class="fas fa-circle-notch fa-spin ml-2"
            ></i>
          </p>
        </a>
        <ul
          class="nav nav-treeview"
          [collapse]="collapseSubMenuDrive"
          [isAnimated]="true"
        >
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesDrive.Default')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDriveSelecionado(EnumAbaMenuDrive.meuDrive)
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDrive,
                  undefined,
                  EnumAbaMenuDrive.meuDrive
                )
              "
              title="Meu Drive"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-folder-open nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Meu Drive
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesDrive.Default')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDriveSelecionado(EnumAbaMenuDrive.favorito)
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDrive,
                  undefined,
                  EnumAbaMenuDrive.favorito
                )
              "
              title="Favoritos"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-star nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Favoritos
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesDrive.Default')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verficarSubItemDriveSelecionado(EnumAbaMenuDrive.integracao)
              "
              (click)="
                acessarModulo(
                  EnumModulo.TsDrive,
                  undefined,
                  EnumAbaMenuDrive.integracao
                )
              "
              title="Recebidos via integração"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-envelope-open nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Integrados
              </p>
            </a>
          </li>
        </ul>
      </li>

      <li class="nav-item mt-2" [hidden]="!verificarSeMenuEstaAberto()">
        <small class="pl-4" style="display: inline; opacity: 0.6"
          >Configurações</small
        >
        <hr class="ml-4 mb-0 mt-0" />
      </li>

      <li class="nav-item has-treeview">
        <a
          class="nav-link"
          href="javascript:;"
          (click)="collapseSubMenuConta = !collapseSubMenuConta"
          data-toggle="tooltip"
        >
          <i class="fa-cog fas nav-icon"></i>
          <p [hidden]="!verificarSeMenuEstaAberto()">
            <i
              [hidden]="!collapseSubMenuConta"
              class="right fas fa-angle-down"
            ></i>
            <i
              [hidden]="!!collapseSubMenuConta"
              class="right fas fa-angle-up"
            ></i>
            <span class="label-item-menu" style="display: inline">Conta</span>
          </p>
        </a>
        <ul
          class="nav nav-treeview"
          [collapse]="collapseSubMenuConta"
          [isAnimated]="true"
        >
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.Users')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/users')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/users')"
              title="Usuários"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fa-users fas nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Usuários
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.Roles')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/roles')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/roles')"
              title="Perfis"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fa-theater-masks fas nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Perfis
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.TenantEmpresa')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/empresas')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/empresas')"
              title="CNPJs"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fa-industry fas nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                CNPJs
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.TenantParametro')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/configurar-tenant')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/configurar-tenant')"
              title="Configurações"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fa-building fas nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Configurações
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.UsuarioEmpresa')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link sub-item"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/acesso-empresas')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/acesso-empresas')"
              title="Acesso às empresas"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fa-door-open fas nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Acesso às empresas
              </p>
            </a>
          </li>
        </ul>
      </li>

      <li class="nav-item has-treeview" *ngIf="vefificarAcessoHost()">
        <a
          class="nav-link"
          href="javascript:;"
          (click)="collapseSubMenuTenants = !collapseSubMenuTenants"
          data-toggle="tooltip"
        >
          <i class="fas fa-building nav-icon"></i>
          <p [hidden]="!verificarSeMenuEstaAberto()">
            <i
              [hidden]="!collapseSubMenuTenants"
              class="right fas fa-angle-down"
            ></i>
            <i
              [hidden]="!!collapseSubMenuTenants"
              class="right fas fa-angle-up"
            ></i>
            <span class="label-item-menu" style="display: inline">Tenants</span>
          </p>
        </a>
        <ul
          class="nav nav-treeview"
          [collapse]="collapseSubMenuTenants"
          [isAnimated]="true"
        >
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.Tenants')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/tenants')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/tenants')"
              title="Tenants"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-building nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Tenants
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.HostEmpresa')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/empresa-host')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/empresa-host')"
              title="Empresas"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-industry nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Empresas
              </p>
            </a>
          </li>
          <li
            class="nav-item"
            *ngIf="permission.isGranted('PagesPortal.HostEmpresaModulo')"
          >
            <a
              data-toggle="tooltip"
              class="nav-link"
              [class.selecionado]="
                verificarSeMenuEstaAtivo('/app/plataforma/empresa-modulo')
              "
              [href]="tratarAppPathDaRota('/app/plataforma/empresa-modulo')"
              title="Empresa / Módulo"
              [ngClass]="{
                'margem-lateral-submenu': verificarSeMenuEstaAberto()
              }"
            >
              <i class="fas fa-door-open nav-icon"></i>
              <p
                class="label-item-menu"
                [hidden]="!verificarSeMenuEstaAberto()"
                style="display: inline"
              >
                Empresa / Módulo
              </p>
            </a>
          </li>
        </ul>
      </li>

      <li class="nav-item mt-2" [hidden]="!verificarSeMenuEstaAberto()">
        <small class="pl-4" style="display: inline; opacity: 0.6"
          >Suporte</small
        >
        <hr class="ml-4 mb-0 mt-0" style="border-top: none" />
      </li>

      <li class="nav-item">
        <span
          (click)="abrirModalAjuda()"
          data-toggle="tooltip"
          class="nav-link"
          title="Ajuda"
          style="cursor: pointer"
        >
          <i class="fa-headset fas nav-icon"></i>
          <p
            class="label-item-menu"
            [hidden]="!verificarSeMenuEstaAberto()"
            style="display: inline"
          >
            Ajuda
          </p>
        </span>
      </li>
    </ul>
  </nav>
</aside>
