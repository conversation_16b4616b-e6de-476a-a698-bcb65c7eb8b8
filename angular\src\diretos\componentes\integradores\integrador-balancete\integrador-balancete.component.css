.form-tipo-regra {
  z-index: 500;
  position: fixed;
  bottom: 0;
  right: 3rem;
}

.linha-arquivo {
  margin: 0 !important;
}

.card {
  padding: 1rem;
}

.linha-tabela {
  padding: 0;
}

.coluna-botao-upload {
  text-align: center !important;
}

.conteudo-arquivo {
  padding: 0.5rem;
}

.botao-clicavel.active {
  background: #000646 !important;
  color: white !important;
}

.botao-clicavel {
  background: #f3d2be;
  color: white;
  padding: 0.2rem;
}

.fa-times {
  cursor: pointer !important;
}

.above-scroller {
  overflow-x: scroll;
  overflow-y: hidden;
  height: 20px;
  width: 100%;
}

.scroller {
  height: 20px;
}

.texto-arquivo {
  width: 100%;
  height: 100%;
  overflow: auto;
}

.icone-loader {
  color: white;
  margin-left: 0.5rem;
}

input[type="file"] {
  display: none;
}

.label-arquivo {
  background-color: #000646;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  padding: 6px 10px;
}

.botao-incluir-agrupamento {
  font-size: 12px;
  margin-top: 0.2rem;
  padding: 0.2rem;
  margin-bottom: 0.2rem;
}

.fa-exclamation-triangle {
  color: orange;
  font-size: 1.5rem;
  padding: 1rem;
}

.texto-alerta-tratamento-dados {
  font-size: 80%;
  display: block;
}

.icone-modal-tratamento {
  color: #1976d2;
  cursor: pointer;
}

.fa-times {
  color: #f44336;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: white;
  border-color: #80808052;
}

/* .cdk-drag-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 0;
    list-style: none;
}

.cdk-drag-item {
    background-color: #ffffff;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: grab;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.cdk-drag-item:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.cdk-drag-item:active {
    cursor: grabbing;
}

.cdk-drag-preview {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    opacity: 0.9;
} */

.cdk-drag-placeholder {
  background-color: #e0e0e0;
  border: 2px dashed #aaa;
  border-radius: 8px;
  pointer-events: fill !important;
}

.cdk-drag {
  cursor: grab !important;
}
