.titulo {
  display: block;
}

.descricao {
  font-size: 12px;
}

.icone-abrir-pesquisar-grupo-contabil {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.linha-divisao {
  margin-top: 0.5rem !important;
  margin-bottom: 1rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.selecionar-todas-contas {
  cursor: pointer;
}

.btn-primary {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.alinhamento-direita {
  display: flex;
  justify-content: flex-end;
}

.titulo {
  display: block;
}

.descricao {
  font-size: 12px;
}

.table td {
  padding-top: 0.1rem !important;
  padding-bottom: 0.1rem !important;
}

.form-control form-control-sm {
  max-width: 150%;
}

.disabled {
  background: #ccc !important;
}

.icone-collapse {
  margin-left: 0.5rem;
}

.link-collapse {
  cursor: pointer;
}

.label-contabilizacao {
  position: absolute;
  top: -3rem;
  font-weight: bold;
}

.info-contabilizacao {
  position: absolute;
  top: -3.5rem;
  color: green;
  right: 15%;
  font-size: 12px;
  font-weight: bold;
}

.info-recontabilizar {
  position: absolute;
  top: -2.3rem;
  color: orange;
  right: 13%;
  font-size: 12px;
  font-weight: bold;
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 27px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  -webkit-transition: 0.4s;
  transition: 0.4s;
  width: 2.2rem;
  height: 1.4rem;
}

.slider:before {
  position: absolute;
  content: "";
  height: 15px;
  width: 15px;
  left: 2px;
  bottom: 4px;
  background-color: white;
  -webkit-transition: 0.4s;
  transition: 0.4s;
}

input:checked + .slider:before {
  -webkit-transform: translateX(17px);
  -ms-transform: translateX(17px);
  transform: translateX(17px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 34px;
}

.slider.round:before {
  border-radius: 50%;
}

.campoComMascara {
  text-align: center;
}

.titulo-tabela {
  display: inline-block;
  width: 12%;
}

.titulo-tabela-principal {
  display: inline-block;
  width: 28%;
}

.btn-outline-primary {
  background-color: #f3d2be !important;
  border-color: #f3d2be;
  color: white !important;
}

.botao-mes-ativo {
  background-color: #f3d2be !important;
  color: white !important;
  border-color: #f3d2be;
}

.botao-mes-selecionado {
  background-color: #000646 !important;
  color: white;
  border-color: #f3d2be;
}

.botao-mes-inativo {
  background-color: #ccc !important;
  color: white;
  border-color: white;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.form-check-input {
  width: 3rem;
  height: 1.5rem;
}

.com-borda-esquerda {
  border-left: 2px solid rgba(0, 0, 0, 0.1);
  font-size: 80%;
}
