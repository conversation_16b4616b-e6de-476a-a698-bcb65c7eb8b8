import { <PERSON><PERSON><PERSON>cyP<PERSON>e, PercentPipe } from "@angular/common";
import {
  Component,
  ElementRef,
  Injector,
  Input,
  OnInit,
  ViewChild,
} from "@angular/core";
import {
  Apuracao,
  ApuracaoAgrupadaCnpj,
  ApuracaoGrid,
} from "@diretos/entidades/apuracao";
import {
  CardPfBn,
  CardValoresRetidoFonte,
  DadosCardImpostoRecolher,
  DadosCards,
  DadosGraficoPermanentesTemporarios,
  DadosGraficoResumoApuracao,
  TotaisAdicaoExclusao,
  TotaisImpostoAPagar,
} from "@diretos/entidades/dashboard";
import { EmpresaUsuarioModulo } from "@diretos/entidades/parametros-tenant/parametrizacao-usuario-cnpj";
import { EnumTipoCalculo } from "@diretos/enums/contabilizacao/enum-calculo";
import { EnumFormaApuracao } from "@diretos/enums/enum-forma-aputacao";
import { EnumLivroTributo } from "@diretos/enums/enum-livro-tributo";
import { EnumMes } from "@diretos/enums/enum-mes";
import { EnumTrimestre } from "@diretos/enums/enum-trimestre";
import { ApuracaoServer } from "@diretos/servicos/apuracao-server";
import { DashboardServer } from "@diretos/servicos/dashboard-server";
import { ParametrizacaoUsuarioCnpjServer } from "@diretos/servicos/parametrizacao-usuario-cnpj-server";
import { Utils } from "@diretos/utils/utils";
import { UtilsMascara } from "@diretos/utils/utils-mascara";
import { AppComponentBase } from "@shared/app-component-base";
import { EnumModulo } from "@shared/plataforma/enums/enum-modulos";
import { DefinicaoTitulosPagina } from "@shared/plataforma/utils/definicao-titulos-pagina";
import { Chart, registerables } from "chart.js";
import { BsModalRef } from "ngx-bootstrap/modal";
import { BehaviorSubject } from "rxjs";
import { DashboardImpostoARecolherComponent } from "../dashboard-imposto-a-recolher/dashboard-imposto-a-recolher.component";
import { DashboardImpostoRetidoComponent } from "../dashboard-imposto-retido/dashboard-imposto-retido.component";
import { DashboardPrejuizoFiscalBaseNegativaComponent } from "../dashboard-prejuizo-fiscal-base-negativa/dashboard-prejuizo-fiscal-base-negativa.component";

@Component({
  selector: "app-dashboard-relatorio-inicial",
  templateUrl: "./dashboard-relatorio-inicial.component.html",
  styleUrl: "./dashboard-relatorio-inicial.component.css",
})
export class DashboardRelatorioInicialComponent
  extends AppComponentBase
  implements OnInit
{
  @ViewChild("apuracaoSelecionadaId") apuracaoSelecionadaId: ElementRef;

  dadosRelatorioAdicoes: ColunasRelatorio[];
  dadosRelatorioExclusoes: ColunasRelatorio[];
  dadosRelatorioPF: ColunasRelatorio[];
  dadosRelatorioBN: ColunasRelatorio[];
  empresasFiltro: EmpresaUsuarioModulo[];
  @Input() tipoCalculo: EnumTipoCalculo;
  _relatorioResumoApuracao: Chart;
  _relatorioPermanentes: Chart;
  _relatorioTemporarios: Chart;
  _relatorioCardImpostoRecolher: Chart;
  _relatorioCardEstimativaRecolhida: Chart;
  _relatorioCardPfBn: Chart;
  _relatorioValorRetidoFonte: Chart;

  palhetaDeCores = [
    /*laranja*/ "#f9ac4e",
    /*cinza*/ "#4D4D4F",
    /*orange*/ "#FF8C61",
    /*grey*/ "#7C898B",
    /*blue*/ "#44C8F5",
  ];

  _apuracaoId: number;
  _carregandoDadosApuracao = false;
  carregandoDadosAdicaoExclusao = false;
  carregandoDadosImpostoAPagar = false;
  carregandoDadosPFBN = false;

  _dadosApuracao: Apuracao;
  _listaApuracoes: ApuracaoAgrupadaCnpj[] = [];
  _dadosCardImpostoRecolher: DadosCardImpostoRecolher;
  _dadosCardEstimativaRecolhida: DadosCards;
  _dadosValorCardPfBn: CardPfBn;
  _dadosValorRetidoFonte: CardValoresRetidoFonte;
  _dadosGraficoPermanentesTemporarios: DadosGraficoPermanentesTemporarios[] =
    [];
  _dadosGraficoResumoApuracao: DadosGraficoResumoApuracao[] = [];
  definicaoTitulosDeTela = new DefinicaoTitulosPagina();
  EnumTipoContaCalculoIrpjCsll = EnumLivroTributo;
  _filtroCnpj: string = "";
  graficoResumoApuracao: any;
  mesesFiltro = [];
  modalImportacaoViaRepositorio: BsModalRef;
  pipe: any = new PercentPipe("pt-BR");
  tipoCalculoSelect = [];
  tituloPaginaAssincrono: BehaviorSubject<string> = new BehaviorSubject<string>(
    ""
  );
  _totaisImpostoAPagar: TotaisImpostoAPagar;
  _totaisAdicaoExclusao: TotaisAdicaoExclusao;
  trimestresFiltro = [];
  utils = new Utils();
  utilsMascara = new UtilsMascara();

  constructor(
    injector: Injector,
    private _apuracaoServer: ApuracaoServer,
    private _parametrizacaoUsuarioCnpjServer: ParametrizacaoUsuarioCnpjServer,
    public _currency: CurrencyPipe,
    private _dashboardServer: DashboardServer
  ) {
    super(injector);
  }

  dadosDashForm = this.formBuilder.group({
    anoBase: [undefined as number],
    apuracaoId: [""],
    filtroApuracaoOficial: [false],
    periodoInicio: [],
    periodoFim: [],
    tipoCalculo: [],
  });

  async ngOnInit(): Promise<void> {
    Chart.register(...registerables);
    this.carregarListaApuracoes();
    this.mesesFiltro = Object.keys(EnumMes).filter(
      (_, i) => parseInt(_, 10) >= 0
    );
    this.trimestresFiltro = Object.keys(EnumTrimestre).filter(
      (_, i) => parseInt(_, 10) >= 0
    );
    this.tipoCalculoSelect = Object.keys(EnumLivroTributo).filter(
      (_, i) => parseInt(_, 10) >= 0
    );
    this.consultarEmpresasAtivas();
  }

  async consultarEmpresasAtivas() {
    await this._parametrizacaoUsuarioCnpjServer
      .getListaEmpresas(this.appSession.userId)
      .toPromise()
      .then((result) => {
        this.empresasFiltro = result.filter((empresa) =>
          empresa.modulos?.find(
            (x) =>
              x.modulo == EnumModulo.TsDiretos &&
              x.possuiAcessoAoModulo &&
              x.ativo
          )
        );
      });
  }

  carregarListaApuracoes() {
    if (this.validarFiltroPeriodo() == false) {
      this._carregandoDadosApuracao = false;
      return;
    }
    this._apuracaoServer
      .getAllAgrupadas(
        undefined,
        undefined,
        this.dadosDashForm.get("apuracaoId").value,
        undefined,
        1,
        this.definirValorFiltroCnpj(),
        this.dadosDashForm.get("anoBase").value,
        this.getValorFiltroSomenteApuracaoOficial(),
        undefined,
        0,
        10000000
      )
      .toPromise()
      .then((result) => {
        this._listaApuracoes = result.items;
        this.selecionarApuracaoConformeParametro();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  validarFiltroPeriodo() {
    if (
      parseInt(this.dadosDashForm.get("periodoInicio").value) >
      parseInt(this.dadosDashForm.get("periodoFim").value)
    ) {
      this.notify.error("Perído inicial deve ser menor que o final");
      this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
        ? this.dadosDashForm.get("periodoFim").setValue(13)
        : this.dadosDashForm.get("periodoFim").setValue(4);
      this.salvarFiltroLocalStorage();
      return false;
    }
    return true;
  }

  definirValorFiltroCnpj() {
    if (
      localStorage.getItem(
        `filtro_cnpj_dash_usuario${this.appSession.user.id}`
      ) &&
      !this.activeRouter.snapshot.params["id"]
    ) {
      this._filtroCnpj = localStorage.getItem(
        `filtro_cnpj_dash_usuario${this.appSession.user.id}`
      );
      return localStorage.getItem(
        `filtro_cnpj_dash_usuario${this.appSession.user.id}`
      );
    } else return this._filtroCnpj;
  }

  possuiApuracaoSalvaParaOUsuario() {
    if (
      localStorage.getItem(
        `filtro_apuracao_dash_usuario${this.appSession.user.id}`
      )
    ) {
      this._apuracaoId = parseInt(
        localStorage.getItem(
          `filtro_apuracao_dash_usuario${this.appSession.user.id}`
        )
      );
      return true;
    } else return false;
  }

  redefinirFiltroTodasAsEmpresas() {
    this._filtroCnpj = "";
    this.salvarFiltroCnpjLocalStorage();
  }

  selecionarApuracaoConformeParametro() {
    let listaAgrupamentos = this._listaApuracoes.map((x) => x.listaApuracoes);
    let listaApuracoes = [];
    listaAgrupamentos.forEach((x) => listaApuracoes.push(...x));

    if (listaApuracoes.length == 0) return;
    else if (this.activeRouter.snapshot.params["id"]) {
      this._apuracaoId = this.activeRouter.snapshot.params["id"];
      this.redefinirFiltroTodasAsEmpresas();
      let apuracaoSelecionada = listaApuracoes.find(
        (x) => x.id == this._apuracaoId
      );
      apuracaoSelecionada.selecionada = true;
      this.salvarFiltroApuracaoIdLocalStorage();
    } else if (this.possuiApuracaoSalvaParaOUsuario()) {
      let apuracaoSelecionada = listaApuracoes.find(
        (x) => x.id == this._apuracaoId
      );
      if (apuracaoSelecionada) {
        this._apuracaoId = apuracaoSelecionada?.id;
        apuracaoSelecionada.selecionada = true;
      } else {
        this._apuracaoId = listaApuracoes[0]?.id;
        listaApuracoes[0].selecionada = true;
      }
    } else if (!this.activeRouter.snapshot.params["id"] && listaApuracoes[0]) {
      this._apuracaoId = listaApuracoes[0]?.id;
      listaApuracoes[0].selecionada = true;
    }

    this.buscarDadosApuracao();
  }

  posicionarScrollListaApuracaoSelecionada() {
    var linhaApuracao = document.getElementById(`apuracao_${this._apuracaoId}`);
    linhaApuracao.classList.add("linha-apuracao-selecionada");
    linhaApuracao.scrollIntoView({
      behavior: "smooth",
      block: "center",
      inline: "nearest",
    });
  }

  filtrarListaApuracoes(filtro) {
    this._listaApuracoes.map((agrupamento) => {
      agrupamento.listaApuracoes.map((apuracao) => {
        apuracao.apresentarNaLista = true;
      });
    });

    if (filtro !== undefined && filtro !== "")
      this._listaApuracoes.map((agrupamento) => {
        agrupamento.listaApuracoes.map((apuracao) => {
          if (
            !apuracao.apelido.toLowerCase().includes(filtro.toLowerCase()) &&
            !apuracao.razaoSocial
              .toLowerCase()
              .includes(filtro.toLowerCase()) &&
            !apuracao.cnpj.toLowerCase().includes(filtro.toLowerCase())
          )
            apuracao.apresentarNaLista = false;
        });
      });
    else
      this._listaApuracoes.map((agrupamento) => {
        agrupamento.listaApuracoes.map((apuracao) => {
          apuracao.apresentarNaLista = true;
        });
      });
  }

  retornarAgrupamentosFiltrados() {
    return this._listaApuracoes.filter((agrupamento) =>
      agrupamento.listaApuracoes.some((x) => x.apresentarNaLista !== false)
    );
  }

  retornarApuracoesFiltradas(listaApuracoes: ApuracaoGrid[]) {
    return listaApuracoes.filter((x) => x.apresentarNaLista != false);
  }

  getDadosGraficoResumoApuracao(apuracaId: number) {
    this._carregandoDadosApuracao = true;
    this._dashboardServer
      .getDadosGraficoResumoApuracao(
        apuracaId,
        this.dadosDashForm.get("periodoInicio").value,
        this.dadosDashForm.get("periodoFim").value,
        this.dadosDashForm.get("tipoCalculo").value
      )
      .toPromise()
      .then((result) => {
        this._dadosGraficoResumoApuracao = result;
        this.formatarDadosRelatorioResumoApuracao();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  getDadosGraficoPermanentesTemporarios(apuracaId: number) {
    this._carregandoDadosApuracao = true;
    this._dashboardServer
      .getDadosGraficoPermanentesTemporarios(
        apuracaId,
        this.dadosDashForm.get("periodoInicio").value,
        this.dadosDashForm.get("periodoFim").value,
        this.dadosDashForm.get("tipoCalculo").value
      )
      .toPromise()
      .then((result) => {
        this._dadosGraficoPermanentesTemporarios = result;
        this.formatarDadosRelatorioPermanente();
        this.formatarDadosRelatorioTemporarios();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  getDadosCardImpostoRecolher(apuracaId: number) {
    this._carregandoDadosApuracao = true;
    this._dashboardServer
      .getDadosCardImpostoRecolher(
        apuracaId,
        this.dadosDashForm.get("periodoInicio").value,
        this.dadosDashForm.get("periodoFim").value,
        this.dadosDashForm.get("tipoCalculo").value
      )
      .toPromise()
      .then((result) => {
        this._dadosCardImpostoRecolher = result;
        this.formatarDadosCardImpostoRecolher();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  getDadosValorCardPfBn(apuracaId: number) {
    this._carregandoDadosApuracao = true;
    this._dashboardServer
      .getDadosValorCardPfBn(
        apuracaId,
        this.dadosDashForm.get("periodoInicio").value,
        this.dadosDashForm.get("periodoFim").value,
        this.dadosDashForm.get("tipoCalculo").value
      )
      .toPromise()
      .then((result) => {
        this._dadosValorCardPfBn = result;
        this.formatarDadosValorCardPfBn();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  getCardTotaisValoresRetidoFonte(apuracaId: number) {
    this._carregandoDadosApuracao = true;
    this._dashboardServer
      .getTotaisValoresRetidoFonte(
        apuracaId,
        this.dadosDashForm.get("periodoInicio").value,
        this.dadosDashForm.get("periodoFim").value,
        this.dadosDashForm.get("tipoCalculo").value
      )
      .toPromise()
      .then((result) => {
        this._dadosValorRetidoFonte = result;
        this.formatarDadosValorCardValorRetidoFonte();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  buscarDadosApuracao() {
    if (this.validarFiltroPeriodo() == false) {
      this._carregandoDadosApuracao = false;
      return;
    }
    this._carregandoDadosApuracao = true;
    this._apuracaoServer
      .getById(this._apuracaoId)
      .toPromise()
      .then(async (apuracao) => {
        this._dadosApuracao = apuracao;
        localStorage.getItem(
          `filtro_dash_salvo_apuracao_${this._apuracaoId}`
        ) !== null
          ? await this.popularFormularioFiltroComDadosLocalStorage()
          : await this.definirFiltrosPadroes();
        this.getDadosGraficoResumoApuracao(this._apuracaoId);
        this.getDadosGraficoPermanentesTemporarios(this._apuracaoId);
        this.getDadosCardImpostoRecolher(this._apuracaoId);
        this.getDadosValorCardPfBn(this._apuracaoId);
        this.getCardTotaisValoresRetidoFonte(this._apuracaoId);
        this.posicionarScrollListaApuracaoSelecionada();
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => (this._carregandoDadosApuracao = false));
  }

  getValorFiltroSomenteApuracaoOficial() {
    return this.dadosDashForm.get("filtroApuracaoOficial").value == true
      ? true
      : false;
  }

  atualizarGrid() {
    this.salvarFiltroLocalStorage();
    this.carregarListaApuracoes();
  }

  salvarFiltroLocalStorage() {
    localStorage.setItem(
      `filtro_dash_salvo_apuracao_${this._apuracaoId}`,
      JSON.stringify(this.dadosDashForm.value)
    );
  }

  salvarFiltroCnpjLocalStorage() {
    this.removerIdApuracaoUrl();
    localStorage.setItem(
      `filtro_cnpj_dash_usuario${this.appSession.user.id}`,
      this._filtroCnpj
    );
  }

  salvarFiltroApuracaoIdLocalStorage() {
    localStorage.setItem(
      `filtro_apuracao_dash_usuario${this.appSession.user.id}`,
      this._apuracaoId.toString()
    );
  }

  popularFormularioFiltroComDadosLocalStorage() {
    if (
      localStorage.getItem(`filtro_dash_salvo_apuracao_${this._apuracaoId}`)
    ) {
      let filtroSalvo = JSON.parse(
        localStorage.getItem(`filtro_dash_salvo_apuracao_${this._apuracaoId}`)
      );
      if (filtroSalvo["anoBase"])
        this.dadosDashForm.get("anoBase").setValue(filtroSalvo["anoBase"]);
      if (filtroSalvo["apuracaoId"])
        this.dadosDashForm
          .get("apuracaoId")
          .setValue(filtroSalvo["apuracaoId"]);
      if (filtroSalvo["filtroApuracaoOficial"])
        this.dadosDashForm
          .get("filtroApuracaoOficial")
          .setValue(filtroSalvo["filtroApuracaoOficial"]);
      if (filtroSalvo["periodoInicio"])
        this.dadosDashForm
          .get("periodoInicio")
          .setValue(filtroSalvo["periodoInicio"]);
      if (filtroSalvo["periodoFim"])
        this.dadosDashForm
          .get("periodoFim")
          .setValue(filtroSalvo["periodoFim"]);
      if (filtroSalvo["tipoCalculo"])
        this.dadosDashForm
          .get("tipoCalculo")
          .setValue(filtroSalvo["tipoCalculo"]);
    }
  }

  definirFiltrosPadroes() {
    this.dadosDashForm.get("tipoCalculo").setValue(1);
    let periodos = this._dadosApuracao.periodos
      .filter((x) => x.periodoApuracao == true)
      .sort((a, b) => (a.periodo < b.periodo ? -1 : 1));

    let primeiroPeriodo = periodos[0].dataInicioPeriodo;
    let ultimoPeriodo = periodos.slice(-1)[0].dataInicioPeriodo;

    let primeiroMes = this.utils.getNumeroMesData(primeiroPeriodo);
    let ultimoMes = this.utils.getNumeroMesData(ultimoPeriodo);
    if (this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal) {
      this.mesesFiltro = Object.keys(EnumMes)
        .filter((_, i) => parseInt(_, 10) >= 0)
        .filter((m) => parseInt(m) >= primeiroMes && parseInt(m) <= ultimoMes);
      this.mesesFiltro.push(EnumMes.AjusteAnual);
      this.dadosDashForm.get("periodoInicio").setValue(primeiroMes);
      this.dadosDashForm.get("periodoFim").setValue(EnumMes.AjusteAnual);
    } else {
      let primeiroTrimestre = this.utils.getNumeroTrimestreDoMes(primeiroMes);
      let ultimoTrimestre = this.utils.getNumeroTrimestreDoMes(ultimoMes);
      this.trimestresFiltro = Object.keys(EnumTrimestre)
        .filter((_, i) => parseInt(_, 10) >= 0)
        .filter(
          (t) =>
            parseInt(t) >= primeiroTrimestre && parseInt(t) <= ultimoTrimestre
        );
      this.dadosDashForm.get("periodoInicio").setValue(primeiroTrimestre);
      this.dadosDashForm.get("periodoFim").setValue(ultimoTrimestre);
    }
  }

  alterarTipoCalculo(tipoCalculo: number) {
    this.dadosDashForm.get("tipoCalculo").setValue(tipoCalculo);
    this.tipoCalculo = tipoCalculo;
    this.buscarDadosApuracao();
  }

  alterarApuracaoSelecionada(apuracaoSelecionada: ApuracaoGrid) {
    this.removerIdApuracaoUrl();
    this._listaApuracoes.forEach((x) => {
      x.listaApuracoes.forEach((apuracao) => {
        if (apuracao.id === apuracaoSelecionada.id) {
          apuracao.selecionada = !apuracao.selecionada;
          if (apuracao.selecionada) {
            this._apuracaoId = apuracaoSelecionada.id;
            this.salvarFiltroApuracaoIdLocalStorage();
            this.removerSelecaoDeMaisApuracoes(apuracaoSelecionada);
            this.buscarDadosApuracao();
          }
        }
      });
    });
  }

  removerIdApuracaoUrl() {
    if (this.router.url.includes(this._apuracaoId?.toString())) {
      let urlSemParametro = this.router.url.substring(
        0,
        this.router.url.indexOf(this._apuracaoId.toString()) - 1
      );
      this.router.navigateByUrl(urlSemParametro);
    }
  }

  removerSelecaoDeMaisApuracoes(apuracaoSeleciona: ApuracaoGrid) {
    this._listaApuracoes.forEach((x) => {
      x.listaApuracoes.forEach((apuracao) => {
        if (apuracao.id !== apuracaoSeleciona.id) apuracao.selecionada = false;
      });
    });
  }

  mostrarIrpj() {
    return (
      this.dadosDashForm.get("tipoCalculo").value == 1 ||
      this.dadosDashForm.get("tipoCalculo").value == 2
    );
  }

  mostrarCsll() {
    return (
      this.dadosDashForm.get("tipoCalculo").value == 1 ||
      this.dadosDashForm.get("tipoCalculo").value == 3
    );
  }

  apresentarGraficosDesabilitados() {
    return this._listaApuracoes?.length == 0;
  }

  abrirDashboardImpostoRecolher() {
    this.modalImportacaoViaRepositorio = this.modalService.show(
      DashboardImpostoARecolherComponent,
      {
        class: "modal-lg mt-5",
        initialState: {
          _tituloModal: "Imposto a recolher",
          _apuracaoId: this._apuracaoId,
          _dadosApuracao: this._dadosApuracao,
          _periodoInicio: this.dadosDashForm.get("periodoInicio").value,
          _periodoFim: this.dadosDashForm.get("periodoFim").value,
          _tipoCalculo: this.dadosDashForm.get("tipoCalculo").value,
        },
      }
    );
  }

  abrirDashboardPfBn() {
    this.modalImportacaoViaRepositorio = this.modalService.show(
      DashboardPrejuizoFiscalBaseNegativaComponent,
      {
        class: "modal-lg mt-5",
        initialState: {
          _tituloModal: "Consumo prejuízo fiscal e base negativa",
          _apuracaoId: this._apuracaoId,
          _dadosApuracao: this._dadosApuracao,
          _periodoInicio: this.dadosDashForm.get("periodoInicio").value,
          _periodoFim: this.dadosDashForm.get("periodoFim").value,
          _tipoCalculo: this.dadosDashForm.get("tipoCalculo").value,
          _dadosdPfBn: this._dadosValorCardPfBn,
        },
      }
    );
  }

  abrirDashboardValoreRetidoFonte() {
    this.modalImportacaoViaRepositorio = this.modalService.show(
      DashboardImpostoRetidoComponent,
      {
        class: "modal-lg mt-5",
        initialState: {
          _tituloModal: "Imposto retido",
          _apuracaoId: this._apuracaoId,
          _dadosApuracao: this._dadosApuracao,
          _periodoInicio: this.dadosDashForm.get("periodoInicio").value,
          _periodoFim: this.dadosDashForm.get("periodoFim").value,
          _tipoCalculo: this.dadosDashForm.get("tipoCalculo").value,
          _dadosValorRetidoFonte: this._dadosValorRetidoFonte,
        },
      }
    );
  }

  formatarDadosRelatorioResumoApuracao() {
    const resultado = [];
    const baseFiscal = [];
    const imposto = [];
    const labels = [];

    this._dadosGraficoResumoApuracao
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((resumo) => {
        resultado.push(resumo.valorResultado);
        baseFiscal.push(resumo.valorBaseFiscal);
        imposto.push(resumo.valorImposto);
        if (resumo.ajusteAnual == true) {
          labels.push("Ajuste anual");
        } else {
          labels.push(
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(resumo.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  resumo.ordem.toString()
                )
          );
        }
      });
    if (this._relatorioResumoApuracao)
      this.atualizarRelatorioResumoApuracao(
        resultado,
        baseFiscal,
        imposto,
        labels
      );
    else
      this.criarRelatorioResumoApuracao(resultado, baseFiscal, imposto, labels);
  }

  criarRelatorioResumoApuracao(
    dataResultado,
    dataValorBaseFiscal,
    datavalorImposto,
    dataLabels
  ) {
    const data = {
      labels: dataLabels,
      datasets: [
        {
          label: "Resultado",
          data: dataResultado,
          order: 3,
          backgroundColor: "#f3d2be",
        },
        {
          label: "Base Fiscal",
          data: dataValorBaseFiscal,
          order: 2,
          backgroundColor: "grey",
        },
        {
          label: "Imposto",
          data: datavalorImposto,
          order: 1,
          backgroundColor: "#000646",
        },
      ],
    };
    this._relatorioResumoApuracao = new Chart("RelatorioResumoApuracao", {
      type: "bar",
      data: data as any,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: "top",
          },
          title: {
            display: false,
          },
        },
      },
    });
  }

  atualizarRelatorioResumoApuracao(
    dataResultado,
    dataValorBaseFiscal,
    datavalorImposto,
    dataLabels
  ) {
    const data = {
      labels: dataLabels,
      datasets: [
        {
          label: "Resultado",
          data: dataResultado,
          order: 3,
          backgroundColor: "#f3d2be",
        },
        {
          label: "Base Fiscal",
          data: dataValorBaseFiscal,
          order: 2,
          backgroundColor: "grey",
        },
        {
          label: "Imposto",
          data: datavalorImposto,
          order: 1,
          backgroundColor: "#000646",
        },
      ],
    };
    this._relatorioResumoApuracao.data = data as any;
    this._relatorioResumoApuracao.update();
  }

  formatarDadosRelatorioPermanente() {
    const valorPermanente = [];
    const labels = [];

    this._dadosGraficoPermanentesTemporarios
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((resumo) => {
        if (resumo.periodoCalculado)
          valorPermanente.push(resumo.valorPermanente);
        if (resumo.ajusteAnual == true) labels.push("Ajuste anual");
        else
          labels.push(
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(resumo.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  resumo.ordem.toString()
                )
          );
      });

    if (this._dadosApuracao.formaApuracao == EnumFormaApuracao.Trimestral) {
      if (this._relatorioPermanentes)
        this.atualizarRelatorioPermanenteTrimestral(valorPermanente, labels);
      else this.criarRelatorioPermanenteTrimestral(valorPermanente, labels);
    } else if (this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal) {
      //Tratamento para gráfico em cascata
      const dataValorPermanente = [];
      const listaCoresColunasGrafico = [];

      let totalPermanente = 0;
      for (let i = 0; i < valorPermanente.length; i++) {
        if (i == 0) {
          dataValorPermanente.push([0, valorPermanente[i]]);
          valorPermanente[i] < 0
            ? listaCoresColunasGrafico.push("#f3d2be")
            : listaCoresColunasGrafico.push("#000646");
        } else {
          let valorPeriodoAnterior = dataValorPermanente[i - 1][1];
          let valorPeriodoAtual = valorPermanente[i];
          dataValorPermanente.push([valorPeriodoAnterior, valorPeriodoAtual]);
          valorPeriodoAtual < valorPeriodoAnterior
            ? listaCoresColunasGrafico.push("#f3d2be")
            : listaCoresColunasGrafico.push("#000646");
        }
      }
      dataValorPermanente.push(totalPermanente);

      if (this._relatorioPermanentes)
        this.atualizarRelatorioPermanenteAnual(
          dataValorPermanente,
          labels,
          listaCoresColunasGrafico
        );
      else
        this.criarRelatorioPermanenteAnual(
          dataValorPermanente,
          labels,
          listaCoresColunasGrafico
        );
    }
  }

  criarRelatorioPermanenteAnual(
    dataValorPermanente,
    labels,
    listaCoresColunasGrafico
  ) {
    const data = dataValorPermanente;
    this._relatorioPermanentes = new Chart("RelatorioPermanente", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            data: dataValorPermanente,
            backgroundColor: listaCoresColunasGrafico,
            barPercentage: 0.5,
            order: 2,
          },
          {
            data: dataValorPermanente,
            backgroundColor: "grey",
            type: "line",
            order: 1,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioCascata() as any,
    });
  }

  criarRelatorioPermanenteTrimestral(dataValorPermanente, labels) {
    this._relatorioPermanentes = new Chart("RelatorioPermanente", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            data: dataValorPermanente,
            backgroundColor: "#000646",
            barPercentage: 0.2,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioBarraPadrao() as any,
    });
  }

  atualizarRelatorioPermanenteAnual(
    dataValorPermanente,
    labels,
    listaCoresColunasGrafico
  ) {
    const data = {
      labels: labels,
      datasets: [
        {
          data: dataValorPermanente,
          backgroundColor: listaCoresColunasGrafico,
          barPercentage: 0.5,
          order: 2,
        },
        {
          data: dataValorPermanente,
          backgroundColor: "grey",
          type: "line",
          order: 1,
        },
      ],
    };
    this._relatorioPermanentes.data = data as any;
    this._relatorioPermanentes.options =
      this.retornarDefinicoesRelatorioCascata() as any;
    this._relatorioPermanentes.update();
  }

  atualizarRelatorioPermanenteTrimestral(dataValorPermanente, labels) {
    const data = {
      labels: labels,
      datasets: [
        {
          data: dataValorPermanente,
          backgroundColor: "#000646",
          barPercentage: 0.2,
        },
      ],
    };
    this._relatorioPermanentes.data = data as any;
    this._relatorioPermanentes.options =
      this.retornarDefinicoesRelatorioBarraPadrao() as any;
    this._relatorioPermanentes.update();
  }

  formatarDadosRelatorioTemporarios() {
    const valorTemporario = [];
    const labels = [];

    this._dadosGraficoPermanentesTemporarios
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((resumo) => {
        if (resumo.periodoCalculado)
          valorTemporario.push(resumo.valorTemporaria);
        if (resumo.ajusteAnual == true) {
          labels.push("Ajuste anual");
        } else {
          labels.push(
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(resumo.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  resumo.ordem.toString()
                )
          );
        }
      });

    if (this._dadosApuracao.formaApuracao == EnumFormaApuracao.Trimestral) {
      if (this._relatorioTemporarios)
        this.atualizarRelatorioTemporarioTrimestral(valorTemporario, labels);
      else this.criarRelatorioTemporarioTrimestral(valorTemporario, labels);
    } else if (this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal) {
      const dataValorTemporario = [];
      const listaCoresColunasGrafico = [];
      let totalTemporario = 0;

      for (let i = 0; i < valorTemporario.length; i++) {
        if (i == 0) {
          dataValorTemporario.push([0, valorTemporario[i]]);
          valorTemporario[i] < 0
            ? listaCoresColunasGrafico.push("#f3d2be")
            : listaCoresColunasGrafico.push("#000646");
        } else {
          let valorPeriodoAnterior = dataValorTemporario[i - 1][1];
          let valorPeriodoAtual = valorTemporario[i];
          dataValorTemporario.push([valorPeriodoAnterior, valorPeriodoAtual]);
          valorPeriodoAtual < valorPeriodoAnterior
            ? listaCoresColunasGrafico.push("#f3d2be")
            : listaCoresColunasGrafico.push("#000646");
        }
      }

      dataValorTemporario.push(totalTemporario);

      if (this._relatorioTemporarios)
        this.atualizarRelatorioTemporarioAnual(
          dataValorTemporario,
          labels,
          listaCoresColunasGrafico
        );
      else
        this.criarRelatorioTemporarioAnual(
          dataValorTemporario,
          labels,
          listaCoresColunasGrafico
        );
    }
  }

  criarRelatorioTemporarioAnual(
    dataValorTemporario,
    labels,
    listaCoresColunasGrafico
  ) {
    this._relatorioTemporarios = new Chart("RelatorioTemporario", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            data: dataValorTemporario,
            backgroundColor: listaCoresColunasGrafico,
            barPercentage: 0.5,
            order: 2,
          },
          {
            data: dataValorTemporario,
            backgroundColor: "grey",
            type: "line",
            order: 1,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioCascata() as any,
    });
  }

  criarRelatorioTemporarioTrimestral(dataValorTemporario, labels) {
    this._relatorioTemporarios = new Chart("RelatorioTemporario", {
      type: "bar",
      data: {
        labels: labels,
        datasets: [
          {
            data: dataValorTemporario,
            backgroundColor: "#000646",
            barPercentage: 0.2,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioBarraPadrao() as any,
    });
  }

  atualizarRelatorioTemporarioAnual(
    dataValorTemporario,
    labels,
    listaCoresColunasGrafico
  ) {
    const data = {
      labels: labels,
      datasets: [
        {
          data: dataValorTemporario,
          backgroundColor: listaCoresColunasGrafico,
          barPercentage: 0.5,
          order: 2,
        },
        {
          data: dataValorTemporario,
          backgroundColor: "grey",
          type: "line",
          order: 1,
        },
      ],
    };
    this._relatorioTemporarios.data = data as any;
    this._relatorioTemporarios.options =
      this.retornarDefinicoesRelatorioCascata() as any;
    this._relatorioTemporarios.update();
  }

  atualizarRelatorioTemporarioTrimestral(dataValorTemporario, labels) {
    const data = {
      labels: labels,
      datasets: [
        {
          data: dataValorTemporario,
          backgroundColor: "#000646",
          barPercentage: 0.2,
        },
      ],
    };
    this._relatorioTemporarios.data = data as any;
    this._relatorioTemporarios.options =
      this.retornarDefinicoesRelatorioBarraPadrao() as any;
    this._relatorioTemporarios.update();
  }

  formatarDadosCardImpostoRecolher() {
    const listaValorImpostoApurado = [];
    const listaValorImpostoRecolhido = [];
    const labels = [];

    this._dadosCardImpostoRecolher.listaGrafico
      .sort((a, b) => (a.ordem < b.ordem ? -1 : 1))
      .forEach((imposto) => {
        if (imposto.periodoCalculado) {
          listaValorImpostoApurado.push(imposto.valorImpostoApurado);
          listaValorImpostoRecolhido.push(imposto.valorImpostoRecolhido);
        }
        if (imposto.ajusteAnual == true) labels.push("Ajuste anual");
        else
          labels.push(
            this._dadosApuracao.formaApuracao == EnumFormaApuracao.Mensal
              ? this.utils.getMesAno(imposto.mes)
              : this.utils.getDescricaoEnumTrimestreLabel(
                  imposto.ordem.toString()
                )
          );
      });

    if (this._relatorioCardImpostoRecolher)
      this.atualizarPreviewRelatorioImpostoRecolher(
        listaValorImpostoApurado,
        listaValorImpostoRecolhido,
        labels
      );
    else
      this.criarPreviewRelatorioImpostoARecolher(
        listaValorImpostoApurado,
        listaValorImpostoRecolhido,
        labels
      );
  }

  criarPreviewRelatorioImpostoARecolher(
    listaValorImpostoApurado,
    listaValorImpostoRecolhido,
    labels
  ) {
    this._relatorioCardImpostoRecolher = new Chart(
      "PreviewRelatorioImpostoARecolher",
      {
        type: "bar",
        data: {
          labels: labels,
          datasets: [
            {
              label: "Recolhido",
              data: listaValorImpostoRecolhido,
              backgroundColor: "#000646",
              barPercentage: 0.5,
              type: "bar",
              order: 2,
            },
            {
              label: "Apurado",
              data: listaValorImpostoApurado,
              backgroundColor: "grey",
              type: "line",
              order: 1,
            },
          ],
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false,
            },
            tooltip: {
              yAlign: "bottom",
              callbacks: {},
            },
          },
          scales: {
            y: {
              display: false,
            },
            x: {
              display: false,
            },
          },
        },
      }
    );
  }

  atualizarPreviewRelatorioImpostoRecolher(
    listaValorImpostoApurado,
    listaValorImpostoRecolhido,
    labels
  ) {
    const data = {
      labels: labels,
      datasets: [
        {
          label: "Recolhido",
          data: listaValorImpostoRecolhido,
          backgroundColor: "#000646",
          barPercentage: 0.5,
          type: "bar",
          order: 2,
        },
        {
          label: "Apurado",
          data: listaValorImpostoApurado,
          backgroundColor: "grey",
          type: "line",
          order: 1,
        },
      ],
    };
    this._relatorioCardImpostoRecolher.data = data as any;
    this._relatorioCardImpostoRecolher.update();
  }

  formatarDadosValorCardPfBn() {
    const valoresSaldoInicial = [];
    const valoresMovimantacao = [];
    const valoresSaldoFinal = [];
    let valoresSaldoInicialCascata = [];
    let valoresMovimentacaoCascata = [];
    let valoresSaldoFinalCascata = [];

    valoresSaldoInicial.push(
      this._dadosValorCardPfBn.saldoInicialPf,
      this._dadosValorCardPfBn.saldoInicialBn
    );
    valoresMovimantacao.push(
      this._dadosValorCardPfBn.movimentoPf,
      this._dadosValorCardPfBn.movimentoBn
    );
    valoresSaldoFinal.push(
      this._dadosValorCardPfBn.saldoFinalPf,
      this._dadosValorCardPfBn.saldoFinalBn
    );

    let movimentacaoPF = valoresSaldoInicial[0] + valoresMovimantacao[0];
    let movimentacaoBN = valoresSaldoInicial[1] + valoresMovimantacao[1];

    valoresSaldoInicialCascata.push(
      [0, valoresSaldoInicial[0]],
      [0, valoresSaldoInicial[1]]
    );
    valoresMovimentacaoCascata.push(
      [valoresSaldoInicial[0], movimentacaoPF],
      [valoresSaldoInicial[1], movimentacaoBN]
    );
    valoresSaldoFinalCascata.push(
      [0, valoresSaldoFinal[0]],
      [0, valoresSaldoFinal[1]]
    );

    if (this._relatorioCardPfBn)
      this.atualizarRelatorioValorCardPfB(
        valoresSaldoInicialCascata,
        valoresMovimentacaoCascata,
        valoresSaldoFinalCascata
      );
    else
      this.criarPreviewRelatorioValorCardPfB(
        valoresSaldoInicialCascata,
        valoresMovimentacaoCascata,
        valoresSaldoFinalCascata
      );
  }

  criarPreviewRelatorioValorCardPfB(
    valoresSaldoInicial,
    valoresMovimantacao,
    valoresSaldoFinal
  ) {
    this._relatorioCardPfBn = new Chart("PreviewRelatorioPfBn", {
      type: "bar",
      data: {
        labels: ["Prejuizo Fiscal", "Base negativa"],
        datasets: [
          {
            label: "Saldo Inicial",
            data: valoresSaldoInicial,
            backgroundColor: "#f8d4c0",
            order: 2,
            barPercentage: 0.5,
          },
          {
            label: "Movimento",
            data: valoresMovimantacao,
            backgroundColor: "grey",
            order: 2,
            barPercentage: 0.5,
          },
          {
            label: "Saldo Final",
            data: valoresSaldoFinal,
            backgroundColor: "#000646",
            order: 2,
            barPercentage: 0.5,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioCascataGraficoPreview() as any,
    });
  }

  atualizarRelatorioValorCardPfB(
    valoresSaldoInicial,
    valoresMovimantacao,
    valoresSaldoFinal
  ) {
    const data = {
      labels: ["Prejuizo Fiscal", "Base negativa"],
      datasets: [
        {
          label: "Saldo Inicial",
          data: valoresSaldoInicial,
          backgroundColor: "#f8d4c0",
          order: 2,
          barPercentage: 0.5,
        },
        {
          label: "Movimento",
          data: valoresMovimantacao,
          backgroundColor: "grey",
          order: 2,
          barPercentage: 0.5,
        },
        {
          label: "Saldo Final",
          data: valoresSaldoFinal,
          backgroundColor: "#000646",
          order: 2,
          barPercentage: 0.5,
        },
      ],
    };
    this._relatorioCardPfBn.data = data as any;
    this._relatorioCardPfBn.update();
  }

  formatarDadosValorCardValorRetidoFonte() {
    const valoresRetencao = [];
    const valoresUtilizacao = [];
    const valoresSaldoFinal = [];
    let valoresRetencaoCascata = [];
    let valoresUtilizacaoCascata = [];
    let valoresSaldoFinalCascata = [];

    valoresRetencao.push(
      this._dadosValorRetidoFonte.retencaoIr,
      this._dadosValorRetidoFonte.retencaoCs
    );
    valoresUtilizacao.push(
      this._dadosValorRetidoFonte.utilizacaoIr,
      this._dadosValorRetidoFonte.utilizacaoCs
    );
    valoresSaldoFinal.push(
      this._dadosValorRetidoFonte.saldoIr,
      this._dadosValorRetidoFonte.saldoCs
    );

    let retencaoIR = valoresRetencao[0] - valoresUtilizacao[0];
    let retencaoCS = valoresRetencao[1] - valoresUtilizacao[1];

    valoresRetencaoCascata.push(
      [0, valoresRetencao[0]],
      [0, valoresRetencao[1]]
    );
    valoresUtilizacaoCascata.push(
      [valoresRetencao[0], retencaoIR],
      [valoresRetencao[1], retencaoCS]
    );
    valoresSaldoFinalCascata.push(
      [0, valoresSaldoFinal[0]],
      [0, valoresSaldoFinal[1]]
    );

    if (this._relatorioValorRetidoFonte)
      this.atualizarRelatorioValorCardValorRetidoFonte(
        valoresRetencaoCascata,
        valoresUtilizacaoCascata,
        valoresSaldoFinalCascata
      );
    else
      this.criarPreviewRelatorioValorCardValorRetidoFonte(
        valoresRetencaoCascata,
        valoresUtilizacaoCascata,
        valoresSaldoFinalCascata
      );
  }

  criarPreviewRelatorioValorCardValorRetidoFonte(
    valoresRetencao,
    valoresUtilizacao,
    valoresSaldoFinal
  ) {
    this._relatorioValorRetidoFonte = new Chart(
      "PreviewRelatorioValorRetidoFonte",
      {
        type: "bar",
        data: {
          labels: ["IRPJ", "CSLL"],
          datasets: [
            {
              label: "Retenção",
              data: valoresRetencao,
              backgroundColor: "#f8d4c0",
              barPercentage: 0.5,
              order: 1,
            },
            {
              label: "Utilização",
              data: valoresUtilizacao,
              backgroundColor: "grey",
              barPercentage: 0.5,
              order: 1,
            },
            {
              label: "Saldo Final",
              data: valoresSaldoFinal,
              backgroundColor: "#000646",
              barPercentage: 0.5,
              order: 1,
            },
          ],
        },
        options: this.retornarDefinicoesRelatorioCascataGraficoPreview() as any,
      }
    );
  }

  atualizarRelatorioValorCardValorRetidoFonte(
    valoresRetencao,
    valoresUtilizacao,
    valoresSaldoFinal
  ) {
    const data = {
      labels: ["IRPJ", "CSLL"],
      datasets: [
        {
          label: "Retenção",
          data: valoresRetencao,
          backgroundColor: "#f8d4c0",
          barPercentage: 0.5,
          order: 1,
        },
        {
          label: "Utilização",
          data: valoresUtilizacao,
          backgroundColor: "grey",
          barPercentage: 0.5,
          order: 1,
        },
        {
          label: "Saldo Final",
          data: valoresSaldoFinal,
          backgroundColor: "#000646",
          barPercentage: 0.5,
          order: 1,
        },
      ],
    };
    this._relatorioValorRetidoFonte.data = data as any;
    this._relatorioValorRetidoFonte.update();
  }

  customizeLabel(args) {
    return `${(args.percent * 100).toFixed(2)}%`;
  }

  customizeTooltip = (args: any) => ({
    text: `${args.argumentText}: ${this._currency.transform(args.valueText)}`,
  });

  retornarDefinicoesRelatorioBarraPadrao() {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: false,
        },
      },
    };
  }

  retornarDefinicoesRelatorioCascata() {
    const currency = this._currency;
    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 0.1,
        onComplete: function () {
          var chart = this;
          var ctx = chart.ctx;
          ctx.textAlign = "center";
          ctx.textBaseline = "bottom";

          this.data.datasets.forEach(function (dataset, i) {
            var meta = chart.getDatasetMeta(i);
          });
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          yAlign: "bottom",
          callbacks: {
            label: function (tooltipItem) {
              if (tooltipItem.dataIndex == 0)
                return currency.transform(
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1].toString(),
                  " ",
                  "symbol"
                );
              else {
                let valorPeriodoAnterior =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][0];
                let valorPeriodoAtual =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1];
                let valorDiferenca = valorPeriodoAtual - valorPeriodoAnterior;
                return `${currency.transform(
                  valorDiferenca,
                  " ",
                  "symbol"
                )} | Total: ${currency.transform(
                  valorPeriodoAtual,
                  " ",
                  "symbol"
                )}`;
              }
            },
          },
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };
  }

  retornarDefinicoesRelatorioCascataGraficoPreview() {
    const currency = this._currency;
    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 0.1,
        onComplete: function () {
          var chart = this;
          var ctx = chart.ctx;
          ctx.textAlign = "center";
          ctx.textBaseline = "bottom";

          this.data.datasets.forEach(function (dataset, i) {
            var meta = chart.getDatasetMeta(i);
          });
        },
      },
      plugins: {
        legend: {
          display: false,
        },
        tooltip: {
          yAlign: "bottom",
          callbacks: {
            label: function (tooltipItem) {
              if (tooltipItem.datasetIndex == 1) {
                let valorMovimentoInicial =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][0];
                let valorMovimentoFinal =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1];
                let valorDiferenca =
                  valorMovimentoFinal - valorMovimentoInicial;
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  valorDiferenca,
                  " ",
                  "symbol"
                )}`;
              } else
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1].toString(),
                  " ",
                  "symbol"
                )}`;
            },
          },
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          display: false,
        },
        x: {
          display: false,
        },
      },
    };
  }

  delay(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

export class ColunasRelatorio {
  descricao: string;
  valor: any;
  color: string;
}
