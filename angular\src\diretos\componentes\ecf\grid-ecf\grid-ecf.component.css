.titulo {
  font-size: 13px;
  max-height: 15px;
}

.coluna-ultimo-calculo {
  padding-top: 0;
  padding-bottom: 0;
}

.nav-link {
  display: block;
  padding: 0rem !important;
}

.tabela-apuracao:hover {
  background-color: white;
}

.tabela-filha:hover {
  background-color: white;
}

.icone-dropdown {
  color: black;
  cursor: pointer;
}

.icone-relatorio {
  color: #007bff;
  margin-right: 0.8rem;
  cursor: pointer;
}

.icone-excluir-apuracao {
  color: #f44336;
  margin-right: 0.5rem;
}

.dropdown-item,
.linha-tabela-filha {
  cursor: pointer;
}

.icone-apuracao {
  font-size: 0.9rem;
  color: gray;
}

@media (max-width: 767px) {
  .table-responsive .dropdown-menu {
    position: static !important;
  }
}
@media (min-width: 768px) {
  .table-responsive {
    overflow: visible;
  }
}

.icone-download {
  font-size: 0.8rem;
  color: blue;
  padding-right: 0.4rem;
}

.borda-card {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: #000646;
  border-radius: 0.25rem;
}

th {
  border: none;
}

.titulo {
  font-size: 13px;
  max-height: 15px;
  display: block;
}
