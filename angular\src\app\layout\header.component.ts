import { ChangeDetectionStrategy, Component, Injector } from "@angular/core";
import { AbaLateralService } from "@app/plataforma/componentes/aba-lateral/aba-lateral.service";
import { ModalRelatoriosComponent } from "@diretos/componentes/apuracao/modal-relatorios/modal-relatorios.component";
import { ModalImportacaoComponent } from "@diretos/componentes/importacao/modal-importacao/modal-importacao.component";
import { GridProcessamentoComponent } from "@diretos/componentes/processamento/grid-processamento/grid-processamento.component";
import { AppConsts } from "@shared/AppConsts";
import { PermissoesConsts } from "@shared/PermissoesConsts";
import { AppComponentBase } from "@shared/app-component-base";
import { AppAuthService } from "@shared/auth/app-auth.service";
import { AppSessionService } from "@shared/session/app-session.service";
import { BsModalRef } from "ngx-bootstrap/modal";

@Component({
  selector: "app-header",
  templateUrl: "./header.component.html",
  styleUrls: ["./header.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderComponent extends AppComponentBase {
  loginNameTratado;

  constructor(
    injector: Injector,
    private _abaLateralService: AbaLateralService,
    private _sessionService: AppSessionService,
    private _authService: AppAuthService
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.loginNameTratado = this.appSession.getShownLoginName();
  }

  logout(): void {
    this._authService.logout(
      this._sessionService.tipoLogin,
      this._sessionService.tenantId,
      this._sessionService.userId
    );
  }

  validarApresentacaoBotoesModuloDiretos() {
    return this.router.url.includes("diretos");
  }

  abrirTelaPlataforma() {
    this._abaLateralService.close();
    this.router.navigate(["app/plataforma"]);
  }

  abrirTelaTreinamento() {
    this.router.navigate(["diretos/tsflix"]);
  }

  abrirTelaDashboard() {
    this.router.navigate(["diretos/dashboard"]);
  }

  abrirTelaAplicativo() {
    this.router.navigate(["aplicativo/grid-aplicativos"]);
  }

  abrirModalProcessamento(): void {
    let modalProcessamento: BsModalRef;
    modalProcessamento = this.modalService.show(GridProcessamentoComponent, {
      class: "mw-100 w-75",
      backdrop: "static",
      keyboard: true,
      show: true,
    });
  }

  abrirModalRelatorios(): void {
    if (
      !this.guard.validarPermissaoAcessoPorFuncionalidade(
        PermissoesConsts.RelatoriosApuracao
      )
    )
      return this.guard.abrirBannerContratacaoPacote(
        AppConsts.mensagemAlertaFuncionalidadeNaoInclusaNoPacote
      );

    let modalRelatorios: BsModalRef;
    modalRelatorios = this.modalService.show(ModalRelatoriosComponent, {
      class: "modal-lg",
      backdrop: "static",
      keyboard: true,
      show: true,
    });
  }

  abriModalImportacoes() {
    let modalImportacao: BsModalRef;
    let apuracaoId = undefined;

    if (
      (this.router.url.includes("ecf/passos") ||
        this.router.url.includes("apuracao/passos")) &&
      !this.router.url.includes("bloco-0")
    )
      apuracaoId = this.router.url.split("/")[5];
    else if (this.router.url.includes("bloco-0"))
      apuracaoId = this.router.url.split("/")[4];

    modalImportacao = this.modalService.show(ModalImportacaoComponent, {
      class: "mw-100 w-75",
      backdrop: "static",
      initialState: {
        apuracaoId: apuracaoId,
      },
      keyboard: false,
      show: true,
    });
  }
}
