.borda-card {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: #000646;
  border-radius: 0.25rem;
}

.titulo {
  font-size: 13px;
  max-height: 15px;
  display: block;
}

.input-group-text {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.coluna-linha-divisao {
  border-left: 2px solid rgba(0, 0, 0, 0.1);
}

.icone-padrao {
  font-size: 25px;
}

.cor-status-sucesso {
  color: #4caf50;
}

.cor-status-em-andamento {
  color: #1976d2 !important;
}

.cor-status-negado {
  color: #f44336;
}

.borda-card-padrao {
  border-left: 5px solid white;
  padding-left: 0.5rem !important;
  border-radius: 0.25rem;
}

.card-sucesso {
  border-left-color: #4caf50;
}

.card-em-andamento {
  border-left-color: #1976d2;
}

.card-nao-aprovado {
  border-left-color: #f44336;
}

.valor-negativo {
  color: #f44336;
}

.mensagem-carregando {
  position: absolute;
  top: 55%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.mensagem-carregando-conteudo {
  top: 60% !important;
  font-size: 18px;
}

.texto-link {
  color: #0069c2;
  cursor: pointer;
}

.link-alterar-valor-dcomp {
  color: #0069c2;
  cursor: pointer;
  font-size: 11px;
}

.fa-check {
  color: green;
}

.fa-times {
  color: red;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}
