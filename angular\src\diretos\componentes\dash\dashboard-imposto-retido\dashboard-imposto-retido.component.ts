import { CurrencyPipe } from "@angular/common";
import { Component, Injector, OnInit } from "@angular/core";
import { Apuracao } from "@diretos/entidades/apuracao";
import { CardValoresRetidoFonte } from "@diretos/entidades/dashboard";
import { DashboardServer } from "@diretos/servicos/dashboard-server";
import { Utils } from "@diretos/utils/utils";
import { AppComponentBase } from "@shared/app-component-base";
import { Chart } from "chart.js";

@Component({
  selector: "app-dashboard-imposto-retido",
  templateUrl: "./dashboard-imposto-retido.component.html",
  styleUrls: ["./dashboard-imposto-retido.component.css"],
})
export class DashboardImpostoRetidoComponent
  extends AppComponentBase
  implements OnInit
{
  _apuracaoId: number;
  _dadosApuracao: Apuracao;
  _dadosValorRetidoFonte: CardValoresRetidoFonte;
  _periodoInicio: number;
  _periodoFim: number;
  _tipoCalculo: number;
  _tituloModal: string;
  utils = new Utils();

  constructor(
    injector: Injector,
    private _currency: CurrencyPipe,
    private _dashboardServer: DashboardServer
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.converteDados();
  }

  converteDados() {
    const valoresRetencao = [];
    const valoresUtilizacao = [];
    const valoresSaldoFinal = [];
    let valoresRetencaoCascata = [];
    let valoresUtilizacaoCascata = [];
    let valoresSaldoFinalCascata = [];

    valoresRetencao.push(
      this._dadosValorRetidoFonte.retencaoIr,
      this._dadosValorRetidoFonte.retencaoCs
    );
    valoresUtilizacao.push(
      this._dadosValorRetidoFonte.utilizacaoIr,
      this._dadosValorRetidoFonte.utilizacaoCs
    );
    valoresSaldoFinal.push(
      this._dadosValorRetidoFonte.saldoIr,
      this._dadosValorRetidoFonte.saldoCs
    );

    let retencaoIR = valoresRetencao[0] - valoresUtilizacao[0];
    let retencaoCS = valoresRetencao[1] - valoresUtilizacao[1];

    valoresRetencaoCascata.push(
      [0, valoresRetencao[0]],
      [0, valoresRetencao[1]]
    );
    valoresUtilizacaoCascata.push(
      [valoresRetencao[0], retencaoIR],
      [valoresRetencao[1], retencaoCS]
    );
    valoresSaldoFinalCascata.push(
      [0, valoresSaldoFinal[0]],
      [0, valoresSaldoFinal[1]]
    );

    this.criarPreviewRelatorioValorCardValorRetidoFonte(
      valoresRetencaoCascata,
      valoresUtilizacaoCascata,
      valoresSaldoFinalCascata
    );
  }

  criarPreviewRelatorioValorCardValorRetidoFonte(
    valoresRetencao,
    valoresUtilizacao,
    valoresSaldoFinal
  ) {
    new Chart("RelatorioValorRetidoFonte", {
      type: "bar",
      data: {
        labels: ["IRPJ", "CSLL"],
        datasets: [
          {
            label: "Retenção",
            data: valoresRetencao,
            backgroundColor: "#f8d4c0",
            barPercentage: 0.5,
            order: 1,
          },
          {
            label: "Utilização",
            data: valoresUtilizacao,
            backgroundColor: "grey",
            barPercentage: 0.5,
            order: 1,
          },
          {
            label: "Saldo Final",
            data: valoresSaldoFinal,
            backgroundColor: "#000646",
            barPercentage: 0.5,
            order: 1,
          },
        ],
      },
      options: this.retornarDefinicoesRelatorioCascata() as any,
    });
  }

  customizeLabel(args) {
    return `${(args.percent * 100).toFixed(2)}%`;
  }

  customizeTooltip = (args: any) => ({
    text: `${args.argumentText}: ${this._currency.transform(args.valueText)}`,
  });

  retornarDefinicoesRelatorioCascata() {
    const currency = this._currency;
    return {
      responsive: true,
      maintainAspectRatio: false,
      animation: {
        duration: 0.1,
        onComplete: function () {
          var chart = this;
          var ctx = chart.ctx;
          ctx.textAlign = "center";
          ctx.textBaseline = "bottom";

          this.data.datasets.forEach(function (dataset, i) {
            var meta = chart.getDatasetMeta(i);
          });
        },
      },
      plugins: {
        legend: {
          display: true,
        },
        tooltip: {
          yAlign: "bottom",
          callbacks: {
            label: function (tooltipItem) {
              if (tooltipItem.datasetIndex == 1) {
                let valorMovimentoInicial =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][0];
                let valorMovimentoFinal =
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1];
                let valorDiferenca =
                  valorMovimentoFinal - valorMovimentoInicial;
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  valorDiferenca,
                  " ",
                  "symbol"
                )}`;
              } else
                return `${tooltipItem.dataset.label}: ${currency.transform(
                  tooltipItem.dataset.data[tooltipItem.dataIndex][1].toString(),
                  " ",
                  "symbol"
                )}`;
            },
          },
        },
        title: {
          display: false,
        },
      },
      scales: {
        y: {
          beginAtZero: true,
        },
      },
    };
  }
}

export class ColunasRelatorio {
  descricao: string;
  valor: any;
  color: string;
}
