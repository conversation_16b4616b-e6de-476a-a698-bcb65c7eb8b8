.alerta {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: orange;
  border-radius: 0.25rem;
}

.erro {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: #f44336;
  border-radius: 0.25rem;
}

.acoes {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: #c9cbdb;
  border-radius: 0.25rem;
}

.link-relatorio {
  margin-left: 0.5rem;
  font-size: 0.8rem;
  padding-top: 0.3rem;
}

.fa-spin {
  color: #000646;
}

a {
  color: #007bff;
}

.nome-arquivo {
  border-style: solid;
  border-width: 1px;
  border-radius: 5px;
  padding: 6px;
  border-color: gray;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.mensagem-carregando {
  position: absolute;
  top: 55%;
  left: 50.5%;
  transform: translate(-50%, -50%);
}

.mensagem-carregando-conteudo-tela {
  top: 60%;
  font-size: 18px;
}

.mensagem-carregando-conteudo-modal {
  top: 80%;
  font-size: 18px;
}

.freeze-ui {
  opacity: 0.9 !important;
}

.altura-padrao-tela {
  height: 80vh !important;
}

.tonal-info {
  background-color: #bbdefb !important;
}
