﻿using Ts.Tools.Dev.Email;
using Microsoft.Graph.Models;

namespace Ts.Portal.EmailManager
{
    public class SendMail
    {
        public void SendMailMfa(string destinationEmail, long pin)
        {
        var conteudoMsg =
@"
<html>
<body bgcolor=""#4d4d4f5c"" style=""background-color: #4d4d4f5c"">
    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
        <tbody>
            <tr>
                <td align=""center"" valign=""top"" bgcolor=""#000646"" style=""background-color:#000646"">

                    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding:40px"">
                                    &nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td style=""background-color:#ffffff;padding-top:40px"">&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td align=""center"" valign=""top"">

                    <table align=""center"" bgcolor=""#FFFFFF"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""background-color:#ffffff;max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" bgcolor=""#FFFFFF"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <h1 style=""color: #241c15; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-size: 30px; font-style: normal; font-weight: 400; line-height: 42px; letter-spacing: normal; margin: 0; padding: 0; text-align: center "">Fazendo login?</h1>
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:42px;letter-spacing:normal;margin:0;padding:0;text-align:center"">(" + destinationEmail + @")</p>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                        Você está realizando login na Plataforma Ts, utilize o PIN abaixo para prosseguir com a autenticação.
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""middle"" style=""padding-right:40px;padding-bottom:60px;padding-left:40px"">
                                    <table border=""0"" cellspacing=""0"" cellpadding=""0"">
                                        <tbody>
                                            <tr>
                                                <td align=""center"" bgcolor=""#000646"" id=""m_6565721412097969175button"">
                                                    <span style=""border-radius: 0; color: #ffffff; display: inline-block; font-size: 28px; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-weight: 400; letter-spacing: .3px; padding: 20px; text-decoration: none"" target=""_blank"" >
                                                        " + pin.ToString().Substring(0, 1) + @"&nbsp;&nbsp;" +
                                                            pin.ToString().Substring(1, 1) + @"&nbsp;&nbsp;" +
                                                            pin.ToString().Substring(2, 1) + @"&nbsp;&nbsp;" +
                                                            pin.ToString().Substring(3, 1) + @"&nbsp;&nbsp;" +
                                                            pin.ToString().Substring(4, 1) + @"&nbsp;&nbsp;" +
                                                            pin.ToString().Substring(5, 1) + @"
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""border-top:2px solid #efeeea;color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:12px;font-weight:400;line-height:24px;padding-top:40px;padding-bottom:40px;text-align:center"">
                                </td>
                            </tr>
                            <th style=""height:50px;background-color: #4d4d4f5c""></th>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>
";
            var message = new Message();
            message.Subject = "Está fazendo login?";
            message.Body = new ItemBody
            {
                ContentType = BodyType.Html,
                Content = conteudoMsg
            };
            message.ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = destinationEmail
                        }
                    }
                };

            new AzureEmailManager().Send(message);
        }

        public void SendEmailSuccessRegister(string destinationEmail, string nomeUsuario, string enderecoPlataforma, string senhaUsuario, bool enviarDadosDeAcesso)
        {
            var mensagemOrientacaoFormaAcesso = enviarDadosDeAcesso ? "utilizando os seguintes dados de acesso:" : "utilizando o email e senha informados durante o seu cadastro.";
            var mensagemDadosAcesso = !enviarDadosDeAcesso ? "" :
                @$"<p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center; margin-top:15px"">
                        <b>Email: </b>{destinationEmail};<b>Senha: </b>{senhaUsuario}</p>";

            var conteudoMsg =
@$"<html>
<body bgcolor=""#4d4d4f5c"" style=""background-color: #4d4d4f5c"">
    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
        <tbody>
            <tr>
                <td align=""center"" valign=""top"" bgcolor=""#000646"" style=""background-color:#000646"">

                    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding:40px"">
                                    <img width=""250"" style=""border:0;color:#ffffff;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:12px;font-weight:400;height:auto;letter-spacing:-1px;padding:0;margin:0;outline:none;text-align:center;text-decoration:none""
                                        src = ""data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABIQAAAEKCAMAAABUjn4LAAAAM1BMVEVMaXFMTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU9MTU8syvnAAAAAEHRSTlMAIOCg8BBggMBA0DCQULBwPxGn+wAAAAlwSFlzAAAWJQAAFiUBSVIk8AAAHChJREFUeJztnel28yoMRTPHSZrh/Z/2rjbtVwsDkoDEuHfvn62NHZAPQmJYAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPx91qdf/q/NfXq8kqHo4Rvju6932qs7m/WslXe1lHLbDNvjae17NPzfWF+Pt2Fi+p+ms/+/VcWsInRL3HYwvvxBe/7O1Z4fWnFbSyn374s3t/sldc3Laj1Q3YmR6wzD9nhNvngh44YyNu7Lqugon3N8QZkql/st14FutgelDwss/9agjYKKeKdbNqcIrVO3WV0hXTY8rXPVCjtbJO0ifsg27jt1LELfDMeWVrgZl2y75e+K0OVjYyjy/JHtCbby6rvnBaIE9e2V1ZbPbkze4LbJh5mGPSvLAMreOnttcLez+Af70MB2MWPqX4Q+33zbSofkz7WV+kdFaH+3KNCT8z3d6+0Dy6/1XQO7NfYUjZhRhJKOkKMO1LCQSTm+UD9Y0zgiNsIcJncuQoQ+PbnMZ+BAVoppUPs3RWi9VcOYkm1yWHaRJZm89AyyiXbvjWjOKEK5sZS5D9bHUMaC7lpBHxWlbAIZWooIPR67Bn552NuYLPwPitA67fqnScpQEBaySXuKwG7NI5E2zCdC2eGP3R1Uw0Im8ZChnBgmMUuXshHtuhwRejw21YOy8NszfbJ/T4SOTi/oh23CyQmq1ZrNiRHYre2Tacd8IpRvfbs/qIaFLB/RJJQTYnJQw4G6rItRCUsSocfjo87Vn/Q2ppzlXxOhkz0WFLKLBzYDa7MHHqYlyXezDh6aMZsIKXFgu3eph4UMNq86yiYHNV/KyJaWJUKPc1WIYPqZWzrtPyZCqsOeZYg2QBgWMrdIiLTbNweE5hQhbY6PvSbUsJCep1eLMDmo6rSl4UcOFyZCNb2szM8/sUzB+FMitFbddYVdtBMMDK50FBUU8+aA0JwipHmnjkCb2stoeXrVmbJ1MrdhGIZsUT8f89JEqEaFYtJssPO/JEKnnFHshm/yQhV9RJCLLZOPwKF6d0BoRhFSvYaHIxCh9TPaJ6Te73JQ16f7NlnicyQyiwgdFT41NNk1lPvoMSE05B1mEaGtVkcJ8mHHlK1vbuHqnsv1eEs1QaxXDoI5RY0UhJbeHhCaUYT0MJ0jN6zOM8zXrNoZFuSH9tdt/Cc6ftax8L5fhADYbrlcj1H/qdQ44wbWKuxQX0WihJesVYhr0C25MmN9iC9nik0FCtJaJY0kxxHvDwjNJ0KGx3rWfanF5XxM9ebSqTKX6NQ0eyp1FhFafSlo5DModNPjwfq6WS2/LECEYhqkTQLdH2IdQazSggk+/ioIwqHvDwjNJ0KWYIWnQiucGTU7XzGFfX+YFO7Q1tlE6LM7ntZo0ReamhbfaKV4/yIU0aBw6mqUyMzG6NvdDNfkHjN7QGg2ETI91byMdWVQtfS3n1rKr99pIpSh3odj/1iHVVo0IEv1Do0WSHYvQlMN2lld4VCG4h9TGBZyWuv8AaHZREj78J945oCqYaFUnl5drlFtmEKGPEYyrwhNq6ZgTq5olvFHtfOXFaN3EbpMzNIz83N9M7xcEBby+e1BQKj1Fi5GZhGhzNLVMS5XSP0h8Tz91EoCGnTZ+5GhF48xZxChsBt3tce0hJ3IRtSsMvilcxGa7qngjLmM5lkn1aUiLBR8NG3axM8sImRdyOeqFC0sFJd5LTvfYruo1ery8xiXtzy7CIUq5P9GN+IXjItr4/l3LkKhx392uxr7D/3dgqfYnxEMH1plC9zMIUJGR8jrWmphoZjZa/McN6222vy2ddd3Mr8IBfXjtlKRellLo2/ywfctQuF4tmi/je+ZjrkVUNLfstvsUP92TZhDhOzraFxGoYaFpqF/dblGu0Hyly35woYdiJCcz+UO5Iyfvw1avomP2bUIhRPxC7/y/aC9WhAWstasHDvMFRCaR4RUsVDuT6JuxxE2pPom9btmjh42eD+THkSoZFPEf6zDe8UfWkyL61qEgmFSuafxoX0JgctlM9tOAkLziJBntY7PKrRMV+iIaAO4NgGhf2yd+dMeREi6Qs6XGMf+hslfWoQgehah4NOqmepx2ClvVhAWCgZxswWEPl/lpJCN3N6Vm2OV4XCE3FWjpf6lqmia1Swg9IOz7+9ChERs2ueZ7se3ThfNVU7A+qJnEQoyY1WjHc1ywh2BDFV7c98xH1lvoaTR9KWrY3zfrTr7eeyoaqO3GQfJT7oQIaEkvqDQ+P1/0vvjPq3BYLdjEQrsvOXIPkJgzHrnLXvg2W09T3MR8u0w53SFVGH5FTVVsGYcJD/pQoTkJAZXf7mLvP740yyYdxTSsQi9+fiKwK3XjPfiu3xmWouQzxFyhy+1IdZvnl6brDTnIPlJHyIkcpmeBhct/U+9xspUb/r9ilBg569fnB6cl5F3bToKCFloLULerXa9lqWFhX7K07LzHQyS+xAh8TV5JvyOXahfKx//qHr3oF8RkrHUN5wl6NooWvbAzYOfrWksQu5snDd8qY6ynu+sbabYwyC5DxE6Fb6FuO+3Nidp+yq6FSFp5y1i8CqOIzMCN63vgFB7EfJvOOo+51sp7yn72nKNHgbJfYjQuvAtxi7p2OVpmqXvVoSkq/GeQ5WDuS9przVYMPnimHkD2opQ2hFKeibuabpa1OlmmKvUxSC5DxFalb2F0K6xpAsTqA2V9CpCch7KWxyhybea3CQx2NC18Wy4V9BWhNIRm2tShdxOiRZyPqiDwj5mTSxahMaNINNg56Ly4vQqQrIjfNdWYXvbGUBy2VT3AaHWIpReurpJryhzZ3KzZxB+xXuUuNEsG+1OWbII7WP5+Sdie4/KL6BXEZKd7dvMKehc4xUSpGS6Dwi1FqG0j3LICJTbFVI3CVKYZaPdKUsWITFVIlCahln6TkVIzPB8/RyhX4IwQ+z3rJcWEGosQmmd+ewRk0M1/6Q272QkyTwb7U7pQ4T2RW8x9jXDAFtsJnUhnYqQdDbemeQIwkIRR3NxAaHGIpTew+MjG7T224Z127QYM220O2XBKXrRC4QOv+iL6rzOTkVImt87gy5BWGjqhC0vINRWhDJLV79GzclIjd+f1cJCGToJCHUjQqJXtzb4+MHT1puuri+lUxESlvzeTk0JCwX/XkJAqK0IpfPiT6cwPYjyG0d5WKiTgFA3IlTylQpTn45G2mXp+xQhGRJ6zyShfwRfmZSZ/QIDQk1FKOMInZQLCkaupWGhN5tMjj5EqKSMdH7+SXxFRwF9ipAMCb3kWNcM8ouVAy75v2UEhJqKUFoWfuw0HTMq6C7LwkJvzGSo9CFCsYZSUCdZx9e2FtCnCElnpE2ZdoK+fKw0wYauywgINRWh9OScH4c9nT0r6C6LwkK2dnlT1KgLERK9urEZxItHazQ9i8hHnyIkqv393VoQ97mn/vFuF62cZiKUdoR+v/z0hOqC715bo1r8gy6794yluxAh4VDaks3yxMPoJa2y9H2KkOhuZ5jwkQgLBWu7Owo8aDQTobQj9NtM6Sx9SVOqZ2lMMLXLp4v1lrVlPYiQdE5tXcFBvyW5ssxJnyL0aPTriomf5SO7+J4CDxqtRCizXGtkp0mlKhq/2s8W8rTLl29wfoMv24MIbf31Ixoxdcu43IocdpciVHVCSROipxoGG7ouJiDUUITS5YwjZ+kxW5GF+cJCtnb5ecXh5ZGhDkRIfk+2Tl04oKkJD6Lg8u+0SxGSHvgsH3vkOJ9gi5vlBITaiVDGERoXks7SFym3Lyxkmrk1moA0vLgh5xehoh0pxg9Nx3vG/UN5qrhLEZo3ORZ7h91lwQGhdiKUDjlLO02PoIqCwZ6wkOkBMue2ub+yn5tdhPYle5SKcE+6Tg2BIwP9i9BcoZcgLBTMWFlSQKiZCGWOnz8YLyxLotjDQrbueDL76HZ4mQ7NLUKBBhkdoa3xlrGTVZxB6lKERIc71+ceLiIracpuaCNC6ZmDYX2kXaayNIM1LGRbyhf1rIb7a5bgzCxC4a5LNldULFnIJRHHv674o+hShEStz7ZHZ24QsKiAUCsRyjhCYSeYDh6VuULWsJBJR5KF7baH9kI0qwjtQx/S2KWLl84Ns4zDNsfT6kXIgv6UofqlmpAeBCwrINRKhDKDoomdpucTlS0ttZ3vYfsKsm7V7tbYI5pRhNbHUG6tmwtY8vNPtAVmFroUobPv8peRstaFBYQaiVBm6eo0EpPO0hdWnsXMjE5zeiPsb5oK0VwitD5ExsTGpnacUqYstTfRpQg5L38ZCb+9n71qzLQQoUxDT0tQF9u3/QlfmDe2XxtCTJvtoU0zv1+E9qfr8RZ1Ra0iMa4fzb0ZX9uig0GEAuJhoX72qjHTQIQyshKz0/TYrdBSM89/4jnp8G6KMZ0/GsT+GotQDVYNEt6NNsTNbr9o46+IkPEtvEYV+5R62bzYQwMRymztEzPuTBS7zMPQNzjzNO7euEfIbnud/SiJViJkHiyNx3F6yktf6KrRvwjZTOtFIhQJC3WzebGHBiKUDjXHDzZMZ+mLLNWwp4cvRbw2b1VUN4moFxHamL0U0X/ojWXOpNlKQIQmTMJCCwwINRGhjCMUb1DbWlczFslwDvT2d/OqtFv5ELwTEbrZhVS4/3pblZ4w/Uv/ImRLu75KhCZhoQUGhJqIUOakwYSdpu8ocIVs+7y6DfjyoZyg+I/itR1diNDGYbYi+GbRdevs6iTEhDRkWGiJAaEWIpTxa1KSktENt6Vad7wvaF+zDhVugtaBCG1cqXOxWYRFvKqz9IiQ720K7u+BahHKFJC8PS0cXkMzb/JatnLgcrSVvykxn9lF6OzUhbEm26Yf2lbcp2ksQudBR6+Tjf+lEKEstSKUcYTSgfp0m3i1wr7dfek80v1ha3GIPvwiN68InT+8ATjhwNqcP3FLwSfW/9oxRKgBtSKUTnVl/O9Mlt5naZ6DfyrGy2uDEJ3dM2HmE6Fz0XzL8dOsnYV9lUcUREhFFNDk972fShHKyEk8P/8k7cC4nHbfEYh1mYP1YZsfmu285c8mQmWRdLF1nzWDUJml71+EbMqKCGWpFKHMcChnM5lBnCNO4Tz1p34Oxf76kXukM8TSWITi0Y6oB7c7FsiQaGprVZp3/ojTpQiJhFT1atFj5fuJhq19mZmoE6GMI5S30/S37HCFvOcfNplNuj9sk/6XT4XetXbscjpOBs1ut002tX3DVtFGbunrf2fFmgONJqUhQhHUSsns4ZHv9pxLPaLc02UkaDWN4pJyiFxW9NYFrNewob0PLPyaLzXP7FOEpN3VloYIVYpQbumocm/6TquDe0mWkKbdhNJ1dBKRK7n35lX0p6CpfYMj0dSe7r8qS7+AI39qx/iIUKUIZeJt2tDHtf1HjL11RvOYpktrJs6FM0Tw9q08gjp3qZBwXT3DzuIbJ2/ciwjJGERtmYhQnQjlHCHN3DLRJNuHnJkbkMG8s5CJ0Lnw2dH79xMKBsGeid5jyfdN5hrf6Y3KLeAE1toNhRChOhHKBHZy+fknmaiypS38AaEnjTcmn2w/5HCFZthZMWixwnPlfKG1GhnoU4Sa7nSPCNWJUGZApBtMwZqzESUBoSeNzw6/hCFquyHNsb2r1G7bESSTJ/nGtCJL7zwHsU8REv1nbXoMEaoSodx0ZYOdZmbcqHcXBYSeeLZZtBBOVrL3jLPsMS1HsVafpjA//6RogtGTPkVIKnnDre0QoQj5SskIgeVLzGiYentZQOhJ27DQ9AxB842ziFAQxzOa/bbgnn8Ir7ViKNeNCEk3vDLjigjViFDurB1TbWbC2kp/WRoQetL6vLpgezuzpzXPaRtS+20hLDGi8o8/ShadPelThKTlVk4+Q4RqRChzpy0JksnS5+2tPCD0pHFYKNBEc85ppiN/ZLuZ+nHxpv7aK1h+H3twPyIkHPHKoBAiVCFCOUfIZqe5xa+5/lILCO1OyrrW1mGhwp5xJhGSDWf6hMrz89P7Xd9spyIke506a0KEKkQoc6M1LFK4+lVbO35XT2W1Z4VslC1pnOvwQ1l/hg5DeDIlow/xSz0xlE5FSHafdeMxRKhchHJLV63WktGKjI5p2yLc8mva/l3TELHdePciFLhCuiCLyHvJnHMRU3rvpPKXiJDMydSNxxChchHKrWA322kmS5/soG1ejrbJR9mu0CnER2a2hNnOopdtpz5Z1HiZfIsnOoYvvYqQ7OSq8mOIULEI5Rwhe/Ypk6VPdS/qcavPd1a3O2sbFhJFW2+aTYRk46lBnqr8/JOiDdE6FiGZGqnyqxGhYhHKOUKOqvQvPtMCQj+WqmXx24aFRNHWm2YTIZ8rJCSrdOhROOO6VxEKPO2aZdGIUKkI5fwRzyLFTHwnbu5aQOj34cp8xrLTN1KIoot+/FtFSM5sUrYWEEOP0tkNwue1/9ZuRUj68DUzzxChUhHKaYHHTnOjutjDtYDQ6HtSxm3t7HEVWILZWZhPhIL2y35Doh7LpVtk+cvesycRCqyrwhVChApFKPeB2y1slR/WRbIoakBoHCLMClbtBgwSESLoPjvm+4bu1W/5Rdl8x25FKAhNV+w0jQgVilAu4OIzFd/aDy0gJLv0TJ6+entyiUjRm8OUM4qQwxUS2ejyHr9s5Ue/IhT48OUJMkSoUIRyU5addprJpU++Zi0gFAabk2W3DQgFcmf+VuYUIfM6VqGvNbGPohxbvyIU+PDlFoUIlYlQbg8Pr5069gPRAkKTtHsyT982IBSonblTnFOEzOtYxSNq6q1oyNqxCAXLlovT9IhQmQjlHCF3PWbCPFLQ1lpAaGqkiWFjq0M3fpCzRsyu4KwiFDRiqtnET6s7MqkkS9+xCIV+een0V0SoSISumTv8dpobYwlT1SZBx3rXaJ4++o7rihFa4U5784qQdIVS7SZ+Wt3uA+KBRoe5ZxEK11EX1g4iVCRCuVv8LZHL0o9dFm05WHRUHkunRafF7M/+s+Tjv8DuZs0rQoGoxxtO/DRf3nNKwVr8nkUo7IwL92ZAhEpEKBeaKZlQm8nSj0w15359EQ/FRF42euH28diVOtSyCu2mOLMIWbb0qH/FusK6FqHQzy5TIUSoRIRyd5TYSU7U/pWnBoRSDshktBe98DlUuBUNyeQTHOPRmUXIsqWHqPXaM9sKsvR9i1DoZ+9KRmSIUIEI5YZPZZt+Z4I9/1whLSCU3jg6uDMqEj/h1xJnKEjvOexwbhGS6h8bIJWEcTL4A0x9i9C0/yx4RUSoQIRyS1fL7DSXpf9WBTUglPaEDeukRiHGwWsHwct7xqNzi1DQ9pE3EKHX+i/Yn2rrXISmfvbg7oYRIb8IZR2hwthuZqj1/KjVgFDOgxEyEQ0IibH91jXqCNXxvdsGVoqQtqWH6Ojr8vOR97V8cb2L0LRLdg/JECG/COUcodLFENpyWDUglJ8pNtKYqKsWzibamm1hcvKhqwZmFyFtSw9RfIvTAdzDu+5FKDx1zu9MI0JuEcouIS2105x3tTEEhJR87+9oKxo5ipzdcT5YvOr1tBt0eVHzi1BQ8evMf2vz80+8C9G6F6GYCj0Gsz98uZ9r3088ueD+HnCKUM5pKd9rN+deXdWAkNp2P8OKeOQoXvxN06FrZCKkbxnj/CIU/PbAN9lWv98E4XQaZlT1L0JRFXpsjrrC7q/bcOkBIhRhUilZR6jcTnNZ+iEXt/5CN+ZvS0x4aimRO38cEkGu9WEbqwjncpAORCi3pce+aX4+UqZhwuICRCiuQo/H5uOa/H37030iQKXvJwqo+yWz4ROh7KapFQsfcgMuLSBkiZh+lZ8MQhxy+0QOx+P1dDp9/bjL6XQ9fgyJy725wQ5EKPBsh+S/Wp1aK7wrfUJEYxEqJf/kfdKR39yOh9PoI7qcTvfjNmU/iJCtUnJLV2vsVPV20pjiMJ+h7cwZ9JMAcwnu39+DCGW29Gicn3/i3LB6ESLU6CGIkK1SslpRdX6FcqhqBls0/KBMqq83I//S/B5EKO0KibZukZ+PvLIaQ1uICK20M3+NIEIRwkrJSUXdZoXFtmLdx+WmiNVF27UxT8mM/S5EKLmlh3AO253e7zsncikitNor5yrYQIQiBJWSnTNYZ6f7XNEZGp7ccyj3xh5FS/D7ECHp3f4bIYlcQZv8/BNRy1q1LUaEVqtThfl8M5RE/0UJBff3gEeEctfWnYWbz9Lb37CK/bHQqd6V5QX7EKHAFfrpS16Qn38ichtaHG1BIrRalZrPN/Y5sgJRRlEJ8+MQoZceXxGZMmig7aEZq32RN+Rb6PFLJyIUdYXkNMaWW3K7Mv+LEqHyXsw2sSiOKKawjLlxiFD20mo7LYnJND40Y5WYg5hjVypB3YhQUPPPvLmYO9UqP//E42MtS4RWq/29pBfbfFQkdURJ5cXMil2EsktX6+20IEvvWyVhZX+3J+zP9wrx7UWEIlt6SHel7bn9ntUgSxOhz14sOo81ye52r7NiUXBVSfNhF6Fs1KaBnfr7kPLDnhT2h+iE1oDzse5X9yJCkS09ROCmtb/pWBe7QBH60iGbLZ+3qSn5DkSJ1aVBV6wPyanRnx9mZlK+lcMwoii5+DEuofxFTuNihltYcGutF4/LT6+qryJRQikFO6cftjl/+jwcD41yKuIntSkSumJ/OhyPwzCc/xnPcDseT23HJ/BHuZyOx9swfPdlu0+V2H4uAnpJFAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAODt7O+3oRG3+155+8txGIbtIXeZ5ZKPr0uwFYC/wPHRkt0xVyXr4ftRu3vFJZefSzZXLBBg6ezPTTXo8Xic0y7MZfd72bb4koN+CQAshuYa9HgMqd++3o0vi+rHRVzyEb1EPCzreAFA97QdiynCMMjLYmOp4JJT5JKNvOSCkQEsmd3jBeziFXIJnnSeXnINLok4VYfgkhv2B7Bgwo++EfF48UdY+NSJ2YaXrCeX3MJLsD+ABfOS0VhqPDaEl01T7JMI1XQ8NvHdYkM2AFgI84rQ9LJJSdM0/eQSRAhgwXQvQlNnCREC+EvMK0LT0NHkkqnCbMJLSI8BLJgwYdWIuC5MFG8adZ7ErqcTH8PYdSIVBwDLYOJXtGAT/+nroOhIcj0UxciExpN+CQAsh5fk6FMrugI/J+YvyQT8buorhUO2nbZiFgD6ZjIzp56kbyLXqUXXwFsuEUl6lrACLJ3mKpQZH40lJrFGfnxJYquOy+8lO3bzAFg+16ZxIWV3jeO3GzOkE+v/LkmmvfY/l9xiwzUAWByX+7ERdz1ffvq8LC8en5ccspfsr+olAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAC1ZrVb/ATwVsvKeGFuzAAAAAElFTkSuQmCC"" />
                                </td>
                            </tr>
                            <tr>
                                <td style=""background-color:#ffffff;padding-top:40px"">&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td align=""center"" valign=""top"">

                    <table align=""center"" bgcolor=""#FFFFFF"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""background-color:#ffffff;max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" bgcolor=""#FFFFFF"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <h1 style=""color: #241c15; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-size: 30px; font-style: normal; font-weight: 400; line-height: 42px; letter-spacing: normal; margin: 0; padding: 0; text-align: center "">Olá, {nomeUsuario}!</h1>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                        Estamos muito felizes em tê-lo por aqui (:
                                    </p>
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center; margin-top:15px"">
                                        O seu acesso à Plataforma já está pronto !
                                    </p>
                                   <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center; margin-top:15px"">
                                        Para acessá-la <a href=""{enderecoPlataforma}"">Clique Aqui</a>, ou copie e cole o endereço abaixo em seu navegador {mensagemOrientacaoFormaAcesso}
                                   </p> 
                                   </br>
                                   <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center; margin-top:15px"">
                                        {enderecoPlataforma}
                                   </p>
                                   {mensagemDadosAcesso}
                                   <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center; margin-top:15px"">
                                       Nos vemos lá ! ! !
                                   </p>
                                </td>
                            </tr>
                            <th style=""height:50px;background-color: #4d4d4f5c""></th>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>";

            var message = new Message();
            message.Subject = "Confirmação de Cadastro";
            message.Body = new ItemBody
            {
                ContentType = BodyType.Html,
                Content = conteudoMsg
            };
            message.ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = destinationEmail
                        }
                    }
                };

            new AzureEmailManager().Send(message);
        }

        public void SendMailResetPassword(string destinationEmail, string nomeUsuario, string novaSenha)
        {
            var conteudoMsg =
@$"
<html>
<body bgcolor=""#4d4d4f5c"" style=""background-color: #4d4d4f5c"">
    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
        <tbody>
            <tr>
                <td align=""center"" valign=""top"" bgcolor=""#000646"" style=""background-color:#000646"">

                    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding:40px"">
                                    &nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td style=""background-color:#ffffff;padding-top:40px"">&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td align=""center"" valign=""top"">

                    <table align=""center"" bgcolor=""#FFFFFF"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""background-color:#ffffff;max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" bgcolor=""#FFFFFF"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <h1 style=""color: #241c15; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-size: 30px; font-style: normal; font-weight: 400; line-height: 42px; letter-spacing: normal; margin: 0; padding: 0; text-align: center "">Olá {nomeUsuario}!</h1>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                        Você solicitou uma redefinição de senha em {DateTime.Now.Day.ToString().PadLeft(2, '0')}/{DateTime.Now.Month.ToString().PadLeft(2, '0')}/{DateTime.Now.Year} às {DateTime.Now.Hour.ToString().PadLeft(2, '0')}:{DateTime.Now.Minute.ToString().PadLeft(2, '0')}:{DateTime.Now.Second.ToString().PadLeft(2, '0')}. 
                                    </p>
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                        Esta é sua nova senha:
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""middle"" style=""padding-right:40px;padding-bottom:60px;padding-left:40px"">
                                    <table border=""0"" cellspacing=""0"" cellpadding=""0"">
                                        <tbody>
                                            <tr>
                                                <td align=""center"" bgcolor=""#000646"" id=""m_6565721412097969175button"">
                                                    <span style=""border-radius: 0; color: #ffffff; display: inline-block; font-size: 18px; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-weight: 400; letter-spacing: .3px; padding: 20px; text-decoration: none"" target=""_blank"" >
                                                        " + novaSenha + @"
                                                    </span>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""border-top:2px solid #efeeea;color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:12px;font-weight:400;line-height:24px;padding-top:40px;padding-bottom:40px;text-align:center"">
                                </td>
                            </tr>
                            <th style=""height:50px;background-color: #4d4d4f5c""></th>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>
";
            var message = new Message();
            message.Subject = "Redefinição de senha";
            message.Body = new ItemBody
            {
                ContentType = BodyType.Html,
                Content = conteudoMsg
            };
            message.ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = destinationEmail
                        }
                    }
                };

            new AzureEmailManager().Send(message);

        }

        public void SendMailLockedUser(string destinationEmail, string nomeUsuario, int? segundosNovaTentativa)
        {
            var mensagemAdicional = $"Contate um usuário administrador para realizar o desbloqueio.";
            if (segundosNovaTentativa != null)
                mensagemAdicional = $"Tente novamente daqui a {segundosNovaTentativa / 60} minutos.";

            var conteudoMsg = 
@$"
<html>
<body bgcolor=""#4d4d4f5c"" style=""background-color: #4d4d4f5c"">
    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
        <tbody>
            <tr>
                <td align=""center"" valign=""top"" bgcolor=""#000646"" style=""background-color:#000646"">

                    <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding:40px"">
                                    &nbsp;
                                </td>
                            </tr>
                            <tr>
                                <td style=""background-color:#ffffff;padding-top:40px"">&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            <tr>
                <td align=""center"" valign=""top"">

                    <table align=""center"" bgcolor=""#FFFFFF"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""background-color:#ffffff;max-width:640px"" width=""100%"">
                        <tbody>
                            <tr>
                                <td align=""center"" valign=""top"" bgcolor=""#FFFFFF"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <h1 style=""color: #241c15; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-size: 30px; font-style: normal; font-weight: 400; line-height: 42px; letter-spacing: normal; margin: 0; padding: 0; text-align: center "">Olá {nomeUsuario}!</h1>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                        Após exceder o limite de tentativas de login na TS Plataforma o seu usuário foi bloqueado.
                                    </p>                                    
                                    <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
										{mensagemAdicional}
                                    </p>
                                </td>
                            </tr>
                            <tr>
                                <td align=""center"" valign=""top"" style=""border-top:2px solid #efeeea;color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:12px;font-weight:400;line-height:24px;padding-top:40px;padding-bottom:40px;text-align:center"">
                                </td>
                            </tr>
                            <th style=""height:50px;background-color: #4d4d4f5c""></th>
                        </tbody>
                    </table>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>
";

            var message = new Message();
            message.Subject = "Acesso bloqueado";
            message.Body = new ItemBody
            {
                ContentType = BodyType.Html,
                Content = conteudoMsg
            };
            message.ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = destinationEmail
                        }
                    }
                };

            new AzureEmailManager().Send(message);

        }
    }
}
