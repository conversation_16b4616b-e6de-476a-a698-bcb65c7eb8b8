.tree-view {
  list-style-type: none;
  padding-left: 0 !important;
  height: 50vh;
  overflow-y: scroll;
  overflow-x: hidden;
}

.parent-ul {
  list-style-type: none;
  padding-left: 1rem;
}

.children-ul {
  list-style-type: none;
  padding-left: 1rem;
}

div {
  cursor: pointer;
}

span {
  margin-left: 5px;
}

.folder {
  display: flex;
  align-items: center;
  padding: 5px 20px 5px 20px;
  color: rgb(0, 0, 29);
  margin-right: 0px;
  overflow-y: hidden;
  overflow-x: hidden;
  font-family: "Roboto", sans-serif;
}

.fa-folder {
  color: rgb(0, 0, 29);
  margin-right: 8px;
}

.type-icon {
  margin-right: 8px;
}

.fa {
  font-size: 12px;
}

/* .btn {
  padding: 0.4rem 1rem;
  border-radius: .25rem;
  background-color: #000646 !important;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 12px;
} */

.unselectable {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.nav-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-right: 10px;
}

.form-group {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 5px !important;
}

.form-group input {
  width: 24rem;
}

.btn-save {
  margin-left: 10px;
}

.campo-aviso {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #6c757d;
  margin: 5px;
  width: 100%;
}

.campo-aviso span {
  margin-left: 0 !important;
}

.linha-selecionada {
  background-color: #cfe2ff !important;
}

.modal-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
