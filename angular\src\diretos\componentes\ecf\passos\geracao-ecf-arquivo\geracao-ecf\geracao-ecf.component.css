.icone-sucesso {
  color: green;
  font-size: 1.2rem;
}

.icone-alerta {
  color: orange;
  font-size: 1.2rem;
}

.icone-erro {
  color: #f44336;
  font-size: 1.2rem;
}

.icone-emAndamento {
  color: #007bff;
  font-size: 1.2rem;
}

.icone-pendente {
  color: #c9cbdb;
  font-size: 1.2rem;
}

.status-sucesso {
  color: green;
}

.status-erro {
  color: #f44336;
}

.status-pendente {
  color: black;
}

.status-emAndamento {
  color: #007bff;
}

.dropdown-bloco {
  display: inline-table;
  float: right;
  margin-right: 1rem;
  margin-top: 0.5rem;
  font-size: 20px;
}

.card-sucesso {
  border-left: 5px solid green;
  padding-left: 0.5rem;
  border-radius: 0.25rem;
}

.card-emAndamento {
  border-left: 5px solid #007bff;
  padding-left: 0.5rem;
  border-radius: 0.25rem;
}

.card-erro {
  border-left: 5px solid red;
  padding-left: 0.5rem;
  border-radius: 0.25rem;
}

.card-pendente {
  border-left: 5px solid #c9cbdb;
  padding-left: 0.5rem;
  border-radius: 0.25rem;
}

.card-alerta {
  border-left: 5px solid orange;
  padding-left: 0.5rem;
  border-radius: 0.25rem;
}

.dados-ultima-execucao {
  margin-bottom: 0;
  font-size: 14px;
  margin-left: 1rem;
}

.icone-dropdown {
  color: black;
}

a {
  color: #007bff;
}

.conteudo-app-geracao-ecf {
  overflow-x: hidden;
  overflow-y: visible;
}

.fa-spin {
  color: #000646;
}

.alert-success {
  color: #0f5132;
  background-color: #d1e7dd;
  border-color: #badbcc;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.alert-danger {
  color: #842029;
  background-color: #f8d7da;
  border-color: #f5c2c7;
}

.alert-primary {
  color: #084298;
  background-color: #cfe2ff;
  border-color: #b6d4fe;
}

.tag-alerta {
  background-color: #ffa500;
}

.tag-sucesso {
  background-color: #4caf50;
}

.tag-erro {
  background-color: #f44336;
}

.tag-emAndamento {
  background-color: #1976d2;
}

.tag-naoCalculado {
  background-color: #c9cbdb;
}

.icone-tag-status {
  padding-left: 7px;
  padding-right: 7px;
  padding-top: 2px;
  padding-bottom: 2px;
  color: white;
  font-size: 14px;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}
