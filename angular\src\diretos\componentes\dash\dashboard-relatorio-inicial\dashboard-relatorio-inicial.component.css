.card-laranja {
  background-color: #000646;
  color: white;
}

.card-cinza {
  background-color: #4d4d4f;
  color: white;
}

.card-claro {
  /* background-color: white; */
  color: #4d4d4f;
}

.dimensoes-relatorios-padrao {
  margin-bottom: 2rem !important;
}

.margem-top-padrao {
  margin-top: 2rem !important;
}

.preview-card {
  max-height: 85%;
  margin-top: 1.4rem;
  /* max-width: 95%; */
}

.periodo-card {
  border: solid;
  border-color: rgba(252, 144, 3, 0.863);
  color: rgba(252, 144, 3, 0.842);
  text-align: center;
  max-width: 5rem;
  height: 5rem;
}

.borda-periodo-label {
  padding-top: 1.4rem;
}

.droplist {
  border-radius: 0.3rem;
  max-height: 13rem;
  background-color: rgba(255, 255, 255, 0.675);
}

.card-droplist {
  display: flex;
  flex-direction: column;
  overflow: auto;
  max-height: 12rem;
  min-height: 13rem;
  max-height: 13rem;
}

.cursorPointer {
  cursor: pointer;
}

.align-circle {
  text-align: -webkit-center;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.cor-valor {
  color: #4d4d4f;
}

.margin-inputs {
  margin-left: 0.8rem;
}

.droplist-text {
  font-size: 0.9rem;
}

.texto-descricao-limitada {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.switch-tela-principal {
  font-size: 20px;
}

.form-check-label {
  margin-top: 0.25rem !important;
}

.input-filtro-apuracoes {
  border: 0;
  outline: 0;
  border-radius: 0;
  border-bottom: 1px solid #6c757d7a;
}

.linha-apuracao-selecionada {
  background-color: #f8d4c0;
}

.linha-apuracao {
  cursor: pointer;
}

.linha-apuracao:hover {
  background-color: #f8d4c0;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.relatorio-desabilitado {
  opacity: 0.6;
  pointer-events: none;
}
