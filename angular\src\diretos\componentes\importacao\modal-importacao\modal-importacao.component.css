.conteudo-modal {
  padding: 1.2rem;
}

hr {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.linha-arquivo-importacao {
  max-width: 10px;
  padding-left: 0 !important;
}

.icone-arquivo-importacao {
  font-size: 2.2rem;
  color: green;
}

.importacoes {
  font-size: 12px;
}

.form-importacoes {
  font-size: 12px;
  border-style: dotted;
  border-width: 2px;
  border-color: lightgray;
  padding: 1rem;
  margin-top: 0.5rem;
}

.progress {
  border-radius: 10px !important;
  max-height: 12px;
  margin-top: 2px;
}

.alert-danger {
  color: black;
  background-color: #f8d7da;
  border-color: #dc3545;
  padding: 0.1rem;
  margin: 0;
  padding-left: 0.5rem;
}

.nav-tabs {
  margin-bottom: 0.5rem;
}

.custom-file-input {
  opacity: 0;
  position: absolute;
  z-index: 2;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.icone-loader {
  color: #1976d2;
  margin-left: 0.5rem;
}

.icone-dropdown {
  color: black;
}

.label-arquivo {
  background-color: #000646;
  border-radius: 5px;
  color: #fff;
  cursor: pointer;
  padding: 6px 10px;
}

.botao-incluir-agrupamento {
  position: absolute;
  left: -3.2rem;
  font-size: 12px;
  margin-top: 2px;
  padding: 2px;
}

.badge-warning {
  color: #1f2d3d;
  background-color: #ffc107;
  font-weight: 100 !important;
  font-size: 14px;
}

.badge-secondary {
  color: #fff;
  background-color: #6c757d;
  font-weight: 100;
  font-size: 14px;
}

.mensagem-arquivo-balancete {
  font-size: 13px;
}

.erro {
  color: #f44336;
  font-size: 0.8rem;
}

.alerta {
  color: orange;
  font-size: 0.8rem;
}

.fa-times {
  cursor: pointer;
  color: #f44336;
}

.dropdown-item {
  padding-left: 2rem;
}

.form-row > .col,
.form-row > [class*="col-"] {
  padding-right: 5px !important;
  padding-left: 5px !important;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: white;
  border-color: #80808052;
}
