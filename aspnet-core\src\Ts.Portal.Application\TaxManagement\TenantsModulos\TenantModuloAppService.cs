﻿
using Abp.Application.Services;
using Abp.Authorization;
using Abp.Domain.Uow;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Graph.Models;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using Ts.Portal.MultiTenancy;
using Ts.Portal.TaxManagement.ComunicadorModulos;
using Ts.Portal.TaxManagement.TenantsModulos.Dto;
using Ts.Portal.TaxManagement.TenantsModulos.Interfaces;
using Ts.Tools.Dev;
using Ts.Tools.Dev.Email;

namespace Ts.Portal.TaxManagement.TenantsModulos
{
    [AbpAuthorize]
    public class TenantModuloAppService : ApplicationService
    {
        private readonly ITenantModuloRepository _tenantModuloRepository;
        private readonly TenantManager _tenantManager;
        private readonly GerenciamentoModulo _gerenciamentoModulo;
        private readonly ComunicadorModulo _comunicadorModulo;

        public TenantModuloAppService(ITenantModuloRepository tenantModuloRepository, GerenciamentoModulo gerenciamentoModulo, TenantManager tenantManager,
            ComunicadorModulo comunicadorModulo)
        {
            _comunicadorModulo = comunicadorModulo;
            _tenantModuloRepository = tenantModuloRepository;
            _gerenciamentoModulo = gerenciamentoModulo;
            _tenantManager = tenantManager;
        }

        [UnitOfWork(isTransactional: false)]
        public async Task<TenantModuloDto> UpdateAsync(TenantModuloUpdateDto input)
        {
            var tenantModuloCadastrado = _tenantModuloRepository.GetAll().Where(x => x.TenantIdPortal == input.TenantIdPortal && x.Modulo == input.Modulo).FirstOrDefault();

            if (tenantModuloCadastrado == null)
                tenantModuloCadastrado = _gerenciamentoModulo.CreateModulo(input.Modulo, input.TenantIdPortal, input.Ativo, input.surnameDataBaseProduto, input.surnameDataBaseLog);
            else
            {
                tenantModuloCadastrado.Ativo = input.Ativo;
                _tenantModuloRepository.Update(tenantModuloCadastrado);
            }

            return ObjectMapper.Map(tenantModuloCadastrado, new TenantModuloDto());
        }

        public async Task<IQueryable<TenantModuloDto>> GetAllAsync(TenantModuloResultRequestDto input)
        {
            var registros = _gerenciamentoModulo.ConsultarModulosDaTenant(input.TenantIdPortal);

            return registros.AsQueryable();
        }

        public async Task RegistrarClickModuloRoadmap(string nomeModulo) { }

        [HttpGet]
        public async Task ExecutarCargasTenant(int tenantId, EnumModulo modulo)
        {
            _comunicadorModulo.ExecutarCargasTenantModulo(tenantId, modulo);
        }

        public bool EnviarEmail(EnvioEmailDto envioEmail)
        {
            var conteudoMsg =
                            @"
                            <html>
                            <body bgcolor=""#4d4d4f5c"" style=""background-color: #4d4d4f5c"">
                                <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" width=""100%"">
                                    <tbody>
                                        <tr>
                                            <td align=""center"" valign=""top"" bgcolor=""#000646"" style=""background-color:#000646"">

                                                <table align=""center"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""max-width:640px"" width=""100%"">
                                                    <tbody>
                                                        <tr>
                                                            <td align=""center"" valign=""top"" style=""padding:40px"">
                                                                &nbsp;
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td style=""background-color:#ffffff;padding-top:40px"">&nbsp;</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align=""center"" valign=""top"">

                                                <table align=""center"" bgcolor=""#FFFFFF"" border=""0"" cellpadding=""0"" cellspacing=""0"" style=""background-color:#ffffff;max-width:640px"" width=""100%"">
                                                    <tbody>
                                                        <tr>
                                                            <td align=""center"" valign=""top"" bgcolor=""#FFFFFF"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                                                <h1 style=""color: #241c15; font-family: 'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif; font-size: 20px; font-style: normal; font-weight: 400; line-height: 42px; letter-spacing: normal; margin: 0; padding: 0; text-align: center "">" + envioEmail.TituloEmail + @"</h1>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align=""center"" valign=""top"" style=""padding-right:40px;padding-bottom:40px;padding-left:40px"">
                                                                <p style=""color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:16px;font-style:normal;font-weight:400;line-height:22px;letter-spacing:normal;margin:0;padding:0;text-align:center"">
                                                                    " + envioEmail.ConteudoEmail + @"
                                                                </p>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td align=""center"" valign=""top"" style=""border-top:2px solid #efeeea;color:#6a655f;font-family:'Helvetica Neue',Helvetica,Arial,Verdana,sans-serif;font-size:12px;font-weight:400;line-height:24px;padding-top:40px;padding-bottom:40px;text-align:center"">
                                                            </td>
                                                        </tr>
                                                        <th style=""height:50px;background-color: #4d4d4f5c""></th>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </body>
                            </html>";

            var message = new Message();
            message.Subject = envioEmail.TituloEmail;
            message.Body = new ItemBody
            {
                ContentType = BodyType.Html,
                Content = conteudoMsg
            };
            message.ToRecipients = new List<Recipient>
                {
                    new Recipient
                    {
                        EmailAddress = new EmailAddress
                        {
                            Address = envioEmail.EmailDestino
                        }
                    }
                };

            new AzureEmailManager().Send(message);

            return true;
        }
    }
}
