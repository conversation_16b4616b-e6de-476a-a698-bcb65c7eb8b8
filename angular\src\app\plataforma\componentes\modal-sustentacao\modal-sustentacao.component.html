<abp-modal-header
  [title]="'Sustentação'"
  (onCloseClick)="bsModalRef.hide()"
></abp-modal-header>
<div class="modal-body">
  <section class="content px-2">
    <div class="container-fluid">
      <div
        *ngIf="carregando"
        class="freeze-ui"
        data-text=" "
        style="position: absolute"
      ></div>
      <div class="row justify-content-center">
        <div class="col-6 text-center">
          <p>
            Para abrir um chamado, envie um e-mail para
            <span style="color: #000646">{{
              utils.getContatoEmailSuporte()
            }}</span>
          </p>

          <p *ngIf="!validarApresentacaoAmbienteVendaExecutiva()">
            Você também pode conversar conosco via WhatsApp, é só
            <a target="_blank" [href]="getUrlSuporteWhatsapp()">clicar aqui!</a>
          </p>

          <div *ngIf="validarApresentacaoAmbienteVendaExecutiva()">
            <p>Para assuntos urgentes contate o seu consultor!</p>

            <p class="mb-0">
              E-mail:
              <span style="color: #000646">{{
                utils.getContatoEmailConsultor()
              }}</span>
            </p>
            <p>{{ utils.getNumeroCelularConsultorFormatado() }}</p>
          </div>
        </div>
      </div>
      <!-- <div class="row justify-content-center">
                <div class="col-3 text-center">
                    <button type="button" class="btn btn-sm btn-primary" *ngIf="!apresentarCampoSugestaoMelhoria"
                        (click)="apresentarCampoSugestaoMelhoria = true">Sugerir
                        melhoria <i class="far fa-comment pl-1"></i></button>
                </div>
            </div>
            <div class="row justify-content-center mt-3" *ngIf="apresentarCampoSugestaoMelhoria">
                <label class="pl-0" for="sugestao">Sugestão:</label>
                <textarea class="form-control form-control-sm" id="sugestao" [(ngModel)]="sugestaoMelhoria"></textarea>
            </div> -->
    </div>
  </section>
</div>
<div class="modal-footer justify-content-between">
  <button
    type="button"
    class="btn btn-sm btn-default"
    (click)="bsModalRef.hide()"
  >
    Cancelar
  </button>
  <!-- <button *ngIf="apresentarCampoSugestaoMelhoria" [disabled]="sugestaoMelhoria === ''" type="button"
        class="btn btn-sm btn-primary" (click)="enviarSugestaoMelhoria()">Enviar</button> -->
</div>
