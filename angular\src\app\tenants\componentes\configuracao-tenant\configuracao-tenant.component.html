<section class="content px-2">
  <div class="container-fluid">
    <div class="card">
      <ul class="nav nav-tabs mb-3 mt-3 ml-3" id="myTab" role="tablist">
        <li class="nav-item" role="presentation">
          <a
            class="nav-link"
            [ngClass]="{ active: abaSelecionada == 1 }"
            (click)="alterarAbaSelecionada(1)"
            >Segurança</a
          >
        </li>
        <li
          class="nav-item"
          role="presentation"
          *ngIf="possuiPermissaoDeAcessoAoDiretos"
        >
          <a
            class="nav-link"
            [ngClass]="{ active: abaSelecionada == 2 }"
            (click)="alterarAbaSelecionada(2)"
            >TS - Diretos</a
          >
        </li>
        <li
          class="nav-item"
          role="presentation"
          *ngIf="possuiPermissaoDeAcessoAoDrive"
        >
          <a
            class="nav-link"
            [ngClass]="{ active: abaSelecionada == 3 }"
            (click)="alterarAbaSelecionada(3)"
            >TS - Drive</a
          >
        </li>
      </ul>
      <div class="tab-content">
        <!-- ABA DE SEGURANÇA -->
        <div *ngIf="abaSelecionada == 1">
          <div class="card-body">
            <form
              class="form-horizontal"
              autocomplete="off"
              #Form="ngForm"
              (ngSubmit)="submitFormularioSeguranca()"
              [formGroup]="configuracaoPortalForm"
              [busy]="carregando"
            >
              <div class="row">
                <h5>Autenticação via SSO</h5>
                <hr class="linha-divisao" />

                <div class="col-md-3">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      formControlName="habilitarSSO"
                      type="checkbox"
                      role="switch"
                      id="habilitarSSO"
                      checked
                    />
                    <label class="form-check-label" for="habilitarSSO"
                      >Utilizar a autenticação via SSO</label
                    >
                  </div>
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p class="mb-0" [innerHTML]="getMensagemPadraoCampoSSO()"></p>
                </div>
              </div>
              <div class="row mb-4" *ngIf="getExibirCamposSso()">
                <div class="col-md-3 mb-3">
                  <label
                    for="protocoloSso"
                    data-toggle="tooltip"
                    title="Tipo do segundo fator utilizado para o login. Email: recebimento do código no email cadastrado para o usuário; APP: Recebimento do código no aplicativo parametrizado pelo usuário."
                    style="cursor: pointer"
                    >Protocolo:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <select
                    class="form-select form-control-sm col-10"
                    id="protocoloSso"
                    formControlName="protocoloSso"
                  >
                    <option [ngValue]="null" selected="selected">
                      Selecione
                    </option>
                    <option
                      *ngFor="let indice of protocoloSsoSelect"
                      [value]="indice"
                    >
                      {{ utils.getDescricaoEnumProtocoloSsoLabel(indice) }}
                    </option>
                  </select>
                </div>
                <div class="col-md-3 mb-3">
                  <label
                    for="provedorSso"
                    data-toggle="tooltip"
                    title="Tipo do segundo fator utilizado para o login. Email: recebimento do código no email cadastrado para o usuário; APP: Recebimento do código no aplicativo parametrizado pelo usuário."
                    style="cursor: pointer"
                    >Provedor:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <select
                    class="form-select form-control-sm col-10"
                    id="provedorSso"
                    formControlName="provedorSso"
                  >
                    <option [ngValue]="null" selected="selected">
                      Selecione
                    </option>
                    <option
                      *ngFor="let indice of provedorSsoSelect"
                      [value]="indice"
                    >
                      {{ utils.getDescricaoEnumProvedorSsoLabel(indice) }}
                    </option>
                  </select>
                </div>
                <div
                  class="col-md-6 mb-3"
                  *ngIf="getExibirCamposProtocoloOpenIdConnect()"
                >
                  <label for="clientIdSso" style="cursor: pointer"
                    >Client ID:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <input
                    type="text"
                    name="clientIdSso"
                    id="clientIdSso"
                    formControlName="clientIdSso"
                    class="form-control form-control-sm"
                    maxlength="255"
                  />
                </div>
                <div
                  class="col-md-6 mb-3"
                  *ngIf="getExibirCamposProtocoloOpenIdConnect()"
                >
                  <label for="clientSecretSso" style="cursor: pointer"
                    >Client Secret:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <input
                    type="text"
                    name="clientSecretSso"
                    id="clientSecretSso"
                    formControlName="clientSecretSso"
                    class="form-control form-control-sm"
                    maxlength="255"
                  />
                </div>
                <div class="col-md-6 mb-3">
                  <label for="tenantIdProvedorSso" style="cursor: pointer"
                    >{{ getTextoCampoTenantIdProvedor() }}
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <input
                    type="text"
                    name="tenantIdProvedorSso"
                    id="tenantIdProvedorSso"
                    formControlName="tenantIdProvedorSso"
                    class="form-control form-control-sm"
                    maxlength="255"
                  />
                </div>
                <div
                  class="col-md-12 mb-3"
                  *ngIf="getExibirCamposProtocoloSaml()"
                >
                  <label for="certificateSso" style="cursor: pointer"
                    >Certificate (X.509):
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <textarea
                    type="text"
                    name="certificateSso"
                    id="certificateSso"
                    formControlName="certificateSso"
                    class="form-control form-control-sm"
                    maxlength="2000"
                    rows="4"
                  >
                  </textarea>
                </div>
                <div class="col-md-12 mb-3" *ngIf="getExibirCampoBaseUrl()">
                  <label for="baseUrlSso" style="cursor: pointer"
                    >{{ getTextoCampoBaseUrl() }}
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <input
                    type="text"
                    name="baseUrlSso"
                    id="baseUrlSso"
                    formControlName="baseUrlSso"
                    class="form-control form-control-sm"
                    maxlength="2000"
                  />
                </div>
                <div class="col-md-12" *ngIf="getUrlRedirecionamento() != null">
                  <label
                    class="ml-3"
                    data-toggle="tooltip"
                    class="col-12"
                    [title]="getUrlRedirecionamento()"
                  >
                    <div class="row">
                      <div class="col-12 pr-0">
                        <b>Url de Redirecionamento:</b>
                        {{ getUrlRedirecionamento() }}
                        <i
                          class="far fa-copy pl-2"
                          style="cursor: pointer"
                          (click)="copiarTexto(getUrlRedirecionamento())"
                        ></i>
                      </div>
                    </div>
                  </label>
                </div>
              </div>
              <div class="row" *ngIf="!getExibirCamposSso()">
                <h5>Senhas</h5>
                <hr class="linha-divisao" />

                <div class="form-group col-md-2">
                  <label for="tamanhoSenha" class="obrigatorio"
                    >Tamanho da senha:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="tamanhoSenha"
                    formControlName="tamanhoSenha"
                  >
                    <option
                      *ngFor="let indice of quantidadeCaracteresSenhaSelect"
                      [value]="indice"
                    >
                      {{ indice }}
                    </option>
                  </select>
                </div>
                <div class="form-group col-md-5">
                  <label for="composicaoSenha" class="obrigatorio"
                    >Composição da senha:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="composicaoSenha"
                    formControlName="composicaoSenha"
                  >
                    <option
                      *ngFor="let indice of composicaoSenhaSelect"
                      [value]="indice"
                    >
                      {{ utils.getDescricaoEnumComposicaoSenhaLabel(indice) }}
                    </option>
                  </select>
                </div>
              </div>
              <div class="row" *ngIf="!getExibirCamposSso()">
                <div class="form-group col-md-3">
                  <label for="regraReutilizacaoSenha" class="obrigatorio"
                    >Regra de reutilização da senha:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="regraReutilizacaoSenha"
                    formControlName="regraReutilizacaoSenha"
                  >
                    <option
                      *ngFor="let indice of regraReutilizacaoSenhaSelect"
                      [value]="indice"
                    >
                      {{
                        utils.getDescricaoEnumRegraReutilizacaoSenhaLabel(
                          indice
                        )
                      }}
                    </option>
                  </select>
                </div>
                <div class="form-check form-switch col-md-2 mt-4 pt-2">
                  <input
                    class="form-check-input"
                    formControlName="habilitarValidadeSenha"
                    type="checkbox"
                    role="switch"
                    id="habilitarValidadeSenha"
                    checked
                  />
                  <label class="form-check-label" for="habilitarValidadeSenha"
                    >Habilitar validade da senha</label
                  >
                </div>
                <div
                  class="form-group col-md-2"
                  *ngIf="verificarCampoObrigatorio('diasValidadeSenha')"
                >
                  <label for="diasValidadeSenha" class="obrigatorio"
                    >Validade da senha (dias):</label
                  >
                  <input
                    type="number"
                    class="form-control form-control-sm"
                    name="diasValidadeSenha"
                    id="diasValidadeSenha"
                    required
                    formControlName="diasValidadeSenha"
                    onkeydown="javascript: return event.keyCode == 69 ? false : true"
                  />
                </div>
              </div>
              <div class="row" *ngIf="!getExibirCamposSso()">
                <h5>Autenticação por dois fatores (MFA)</h5>
                <hr class="linha-divisao" />

                <div class="col-md-3">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      formControlName="habilitarMFA"
                      type="checkbox"
                      role="switch"
                      id="habilitarMFA"
                      checked
                    />
                    <label class="form-check-label" for="habilitarMFA"
                      >Utilizar a autenticação por dois fatores</label
                    >
                  </div>
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p class="mb-0" [innerHTML]="getMensagemPadraoCampoMFA()"></p>
                </div>
              </div>
              <div
                class="row"
                *ngIf="
                  verificarCampoObrigatorio('tipoMfa') && !getExibirCamposSso()
                "
              >
                <div class="col-md-3">
                  <label
                    for="tipoMfa"
                    data-toggle="tooltip"
                    title="Tipo do segundo fator utilizado para o login. Email: recebimento do código no email cadastrado para o usuário; APP: Recebimento do código no aplicativo parametrizado pelo usuário."
                    style="cursor: pointer"
                    >Tipo do MFA:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <select
                    class="form-select form-control-sm col-10"
                    id="tipoMfa"
                    formControlName="tipoMfa"
                  >
                    <option [ngValue]="null" selected="selected">
                      Selecione
                    </option>
                    <option
                      *ngFor="let indice of tipoMfaSelect"
                      [value]="indice"
                    >
                      {{ utils.getDescricaoEnumTipoMfaLabel(indice) }}
                    </option>
                  </select>
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p
                    class="mb-0"
                    [innerHTML]="getMensagemPadraoCampoTipoMFA()"
                  ></p>
                </div>
              </div>
              <div
                class="row"
                *ngIf="
                  verificarCampoObrigatorio('dominiosEmailMfa') &&
                  !getExibirCamposSso()
                "
              >
                <div class="col-md-3">
                  <label
                    for="dominiosEmailMfa"
                    data-toggle="tooltip"
                    title='Lista dos domínios permitidos aos usuários (seprado por ";")'
                    style="cursor: pointer"
                    >Domínios do MFA:
                    <i class="fas fa-exclamation" style="color: #000646"></i
                  ></label>
                  <input
                    type="text"
                    class="form-control form-control-sm col-10"
                    name="dominiosEmailMfa"
                    id="dominiosEmailMfa"
                    required
                    maxlength="255"
                    formControlName="dominiosEmailMfa"
                  />
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p
                    class="mb-0"
                    [innerHTML]="getMensagemPadraoCampoDominiosMFA()"
                  ></p>
                </div>
              </div>
              <div class="row" *ngIf="!getExibirCamposSso()">
                <h5>Bloqueio de acesso à conta</h5>
                <hr class="linha-divisao" />

                <div class="col-md-3">
                  <div class="form-check form-switch">
                    <input
                      class="form-check-input"
                      formControlName="possuiBloqueioAcesso"
                      type="checkbox"
                      role="switch"
                      id="possuiBloqueioAcesso"
                      checked
                    />
                    <label class="form-check-label" for="possuiBloqueioAcesso"
                      >Bloquear acesso por tentativa de login inválida</label
                    >
                  </div>
                  <div
                    *ngIf="
                      verificarCampoObrigatorio('quantidadeTentativaBloqueio')
                    "
                  >
                    <label
                      for="quantidadeTentativaBloqueio"
                      data-toggle="tooltip"
                      class="mt-3"
                      title="O número de tentativas até que a conta do usuário seja bloqueada."
                      style="cursor: pointer"
                      >Quantidade limite de tentativas:
                      <i class="fas fa-exclamation" style="color: #000646"></i
                    ></label>
                    <input
                      type="number"
                      class="form-control form-control-sm col-10"
                      id="quantidadeTentativaBloqueio"
                      formControlName="quantidadeTentativaBloqueio"
                    />
                  </div>
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p
                    class="mb-0"
                    [innerHTML]="getMensagemExplicacaoBloqueioConta()"
                  ></p>
                </div>
              </div>
              <div
                class="row mt-2"
                *ngIf="
                  verificarCampoObrigatorio('tipoBloqueioAcesso') &&
                  !getExibirCamposSso()
                "
              >
                <div class="col-md-3">
                  <div class="row">
                    <label
                      for="tipoBloqueioAcesso"
                      data-toggle="tooltip"
                      title="Tipo do bloqueio a ser aplicado na conta do usuário após exceder o limite te tentativas."
                      style="cursor: pointer"
                      >Tipo de bloqueio:
                      <i class="fas fa-exclamation" style="color: #000646"></i
                    ></label>
                    <select
                      class="form-select form-control-sm ml-2 col-11"
                      id="tipoBloqueioAcesso"
                      formControlName="tipoBloqueioAcesso"
                    >
                      <option [ngValue]="null" selected="selected">
                        Selecione
                      </option>
                      <option
                        *ngFor="let indice of tipoBloqueioSelect"
                        [value]="indice"
                      >
                        {{
                          utils.getDescricaoEnumTipoBloqueioAcessoLabel(indice)
                        }}
                      </option>
                    </select>
                  </div>
                  <div
                    class="row mt-3"
                    *ngIf="verificarCampoObrigatorio('tempoBloqueioSegundos')"
                  >
                    <label
                      for="tempoBloqueioSegundos"
                      data-toggle="tooltip"
                      title="Tempo que a conta ficará bloqueada."
                      style="cursor: pointer"
                      >Tempo de bloqueio (hora:minuto):
                      <i class="fas fa-exclamation" style="color: #000646"></i
                    ></label>
                    <input
                      type="text"
                      mask="00:00"
                      class="form-control form-control-sm col-11 ml-2"
                      id="tempoBloqueioSegundos"
                      formControlName="tempoBloqueioSegundos"
                    />
                  </div>
                </div>
                <div class="alert alert-light col-md-9" role="alert">
                  <p
                    class="mb-0"
                    [innerHTML]="getMensagemExplicacaoCampoTipoBloqueio()"
                  ></p>
                </div>
              </div>
            </form>
          </div>
          <div class="card-footer">
            <button
              type="button"
              class="btn btn-sm btn-primary float-right"
              [disabled]="!configuracaoPortalForm.valid || carregando"
              (click)="submitFormularioSeguranca()"
            >
              Salvar
            </button>
            <button
              type="button"
              style="cursor: pointer"
              (click)="abrirVideoAjudaTela()"
              *ngIf="verificarTenantSite()"
              class="btn btn-sm btn-primary float-right mr-2"
            >
              <i class="fab fa-youtube icone-header"></i>
              <span>Ajuda</span>
            </button>
          </div>
        </div>

        <!-- ABA DE PARAMETRIZAÇÕES -->
        <div *ngIf="abaSelecionada == 2">
          <div class="card-body">
            <form
              class="form-horizontal"
              autocomplete="off"
              #Form="ngForm"
              (ngSubmit)="submitFormularioParametrizacaoTsDiretos()"
              [formGroup]="configuracaoDiretosForm"
              [busy]="carregando"
            >
              <h5>Integração de balancete</h5>

              <hr class="linha-divisao" />
              <div class="form-row">
                <div class="col-2">
                  <label for="origemBuscaBalancetes" style="width: 100%"
                    >Origem da busca dos balancetes:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="origemBuscaBalancetes"
                    formControlName="origemBuscaBalancetes"
                  >
                    <option
                      *ngFor="let indice of origemBuscaBalanceteSelect"
                      [value]="indice"
                    >
                      {{
                        utils.getDescricaoEnumEnumOrigemBuscaBalanceteLabel(
                          indice
                        )
                      }}
                    </option>
                  </select>
                </div>
                <div class="col-10">
                  <div class="alert alert-light" role="alert">
                    <p
                      class="mb-0"
                      [innerHTML]="getMensagemPadraoOrigemBuscaBalancete()"
                    ></p>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="col-2">
                  <label for="regraSaldoBalancete" style="width: 100%"
                    >Regra para soma dos saldos:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="regraSaldoBalancete"
                    formControlName="regraSaldoBalancete"
                    data-toggle="tooltip"
                    [title]="getMensagemCampoSomaDeSaldo()"
                    style="cursor: pointer"
                  >
                    <option [ngValue]="null" selected="selected">
                      Selecione
                    </option>
                    <option value="1">Erro</option>
                    <option value="2">Alerta</option>
                  </select>
                </div>
                <div class="col-10">
                  <div class="alert alert-light" role="alert">
                    <p
                      class="mb-0"
                      [innerHTML]="getMensagemPadraoCampoSomaDeSaldo()"
                    ></p>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <div class="col-2">
                  <label for="regraMovimentoBalancete" style="width: 100%"
                    >Regra para soma das movimentação:</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="regraMovimentoBalancete"
                    formControlName="regraMovimentoBalancete"
                    data-toggle="tooltip"
                    [title]="getMensagemCampoSomaDeMovimentacao()"
                    style="cursor: pointer"
                  >
                    <option [ngValue]="null" selected="selected">
                      Selecione
                    </option>
                    <option value="1">Erro</option>
                    <option value="2">Alerta</option>
                  </select>
                </div>
                <div class="col-10">
                  <div class="alert alert-light" role="alert">
                    <p
                      class="mb-0"
                      [innerHTML]="getMensagemPadraoCampoSomaDeMovimentacao()"
                    ></p>
                  </div>
                </div>
              </div>

              <h5>Cálculo da apuração</h5>

              <hr class="linha-divisao" />

              <div class="form-row">
                <div class="col-2">
                  <label for="calculaApuracaoComContasNaoParametrizadas"
                    >Calcular apuração quando existirem contas não
                    parametrizadas?</label
                  >
                  <select
                    class="form-select form-control-sm"
                    id="calculaApuracaoComContasNaoParametrizadas"
                    formControlName="calculaApuracaoComContasNaoParametrizadas"
                  >
                    <option value="true">Sim</option>
                    <option value="false">Não</option>
                  </select>
                </div>
                <div class="col-10">
                  <div class="alert alert-light" role="alert">
                    <p
                      class="mb-0"
                      [innerHTML]="
                        getMensagemPadraoCampoCalculaApuracaoComContasNaoParametrizadas()
                      "
                    ></p>
                  </div>
                </div>
              </div>

              <div class="form-row">
                <div class="col-12">
                  <button
                    type="button"
                    class="btn btn-sm btn-primary float-left"
                    (click)="redefinirPadraoParametrizacao()"
                  >
                    Redefinir parâmetros
                  </button>
                </div>
              </div>
            </form>
          </div>
          <div class="card-footer">
            <button
              type="button"
              class="btn btn-sm btn-primary float-right"
              [disabled]="!configuracaoDiretosForm.valid || carregando"
              (click)="submitFormularioParametrizacaoTsDiretos()"
            >
              Salvar
            </button>
            <button
              type="button"
              style="cursor: pointer"
              (click)="abrirVideoAjudaTela()"
              *ngIf="verificarTenantSite()"
              class="btn btn-sm btn-primary float-right mr-2"
            >
              <i class="fab fa-youtube icone-header"></i>
              <span>Ajuda</span>
            </button>
          </div>
        </div>

        <!-- ABA DE DRIVE -->
        <div *ngIf="abaSelecionada == 3">
          <div class="card-body">
            <h5>API de Integração de documentos</h5>
            <span style="font-size: small">
              Clique
              <a
                href="../../../../assets/pdfs/Documentação API Plataforma TS.docx.pdf"
                style="color: #fd7e14; cursor: pointer"
                download
                >aqui</a
              >
              para baixar a documentação.
            </span>
            <hr class="linha-divisao" />

            <div class="col-6 col-md-6 col-sm-12">
              <div class="row">
                <div class="col-6 pl-0">
                  <label>Nome:</label>
                  <input
                    type="text"
                    [(ngModel)]="tmpCadatroChaveIntegracaoApi.nome"
                    class="form-control form-control-sm"
                    maxlength="255"
                  />
                </div>
                <div class="col-6">
                  <label>Expira em:</label>
                  <div class="row pl-2">
                    <input
                      type="date"
                      [(ngModel)]="tmpCadatroChaveIntegracaoApi.dataExpiracao"
                      (change)="
                        tmpCadatroChaveIntegracaoApi.naoPossuiDataExpiracao = false
                      "
                      class="form-control form-control-sm col-9"
                      [min]="gerarDataAtualFormatada()"
                    />
                    <button
                      class="btn btn-sm btn-primary col ml-3"
                      (click)="adicionarChaveIntegracaoApi()"
                      [disabled]="!validarFormChaveIntegracaoApi()"
                    >
                      Incluir
                    </button>
                  </div>
                  <div class="row form-check form-switch mt-1">
                    <input
                      class="form-check-input"
                      type="checkbox"
                      role="switch"
                      (change)="
                        tmpCadatroChaveIntegracaoApi.dataExpiracao = null
                      "
                      id="habilitarValidadeSenha"
                      [(ngModel)]="
                        tmpCadatroChaveIntegracaoApi.naoPossuiDataExpiracao
                      "
                    />
                    <label
                      class="form-check-label pl-0"
                      for="habilitarValidadeSenha"
                    >
                      <small>Sem data de expiração</small>
                    </label>
                  </div>
                </div>
              </div>
              <div
                class="row ml-1"
                *ngIf="listaChavesIntegracaoApiCadastradas.length > 0"
              >
                <span class="pl-0">Chaves cadastradas</span>
                <hr class="mb-2 mt-0" />
                <div
                  class="row"
                  *ngFor="let chave of listaChavesIntegracaoApiCadastradas"
                >
                  <small
                    data-toggle="tooltip"
                    class="col-3 coluna-descricao-limitada"
                    [title]="chave.nome"
                    ><b>Nome: </b>{{ chave.nome | slice : 0 : 30 }}</small
                  >
                  <small
                    class="ml-3"
                    data-toggle="tooltip"
                    class="col-5"
                    [title]="chave.chave"
                  >
                    <div class="row">
                      <div class="col-11 coluna-descricao-limitada pr-0">
                        <b>Chave:</b>{{ chave.chave }}
                      </div>
                      <div class="col-1 pl-1">
                        <i
                          class="far fa-copy pl-2"
                          style="cursor: pointer"
                          (click)="copiarTexto(chave.chave)"
                        ></i>
                      </div>
                    </div>
                  </small>
                  <small class="col-4" *ngIf="chave.dataExpiracao != null"
                    ><b>Expira em: </b
                    >{{ chave.dataExpiracao | date : "dd/MM/yyyy" }}
                    <button class="btn btn-sm p-0">
                      <i
                        (click)="removerChaveIntegracaoApi(chave)"
                        class="fas fa-trash icone-excluir"
                      ></i></button
                  ></small>
                  <small class="col-4" *ngIf="chave.dataExpiracao == null"
                    ><b>Sem data de expiração</b>
                    <button class="btn btn-sm p-0">
                      <i
                        (click)="removerChaveIntegracaoApi(chave)"
                        class="fas fa-trash icone-excluir"
                      ></i>
                    </button>
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
