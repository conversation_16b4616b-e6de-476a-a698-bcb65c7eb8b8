<section class="content" [busy]="carregando">
  <div class="container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 text-center mb-5">
        <h2>Transformação digital para você</h2>
        <h4><PERSON><PERSON>, vamos mudar o jogo para todas as empresas</h4>
      </div>
    </div>

    <div class="row justify-content-center">
      <div class="col-auto">
        <button
          *ngIf="appSession.tenant"
          class="btn btn-primary"
          (click)="acessarModulo(enumModulo.TsDiretos)"
          type="button"
        >
          <h5 class="mb-2">
            <b>{{ getNomeModuloDiretos() }}</b>
          </h5>
          <p class="mb-1 small">Apuração IR/CS</p>
          <p class="mb-0 small">Geração ECF</p>
        </button>
      </div>
      <div
        class="col-auto"
        *ngIf="
          appSession.tenant && verificarApresentacaoModulo(enumModulo.TsDrive)
        "
      >
        <button
          class="btn btn-primary position-relative"
          (click)="acessarModulo(enumModulo.TsDrive)"
          type="button"
        >
          <span class="badge badge-info tag-beta">Beta</span>
          <h5 class="mb-2"><b>TS Drive</b></h5>
          <p class="mb-1 small">Armazenamento e</p>
          <p class="mb-1 small">compartilhamento de</p>
          <p class="mb-0 small">arquivos</p>
        </button>
      </div>
    </div>

    <div class="row justify-content-center mt-4" *ngIf="carregandoModulos">
      <div class="col-auto">
        <div class="text-center">
          <i class="fas fa-circle-notch fa-spin loading-icon"></i>
          <p class="mt-2 text-muted">Carregando módulos...</p>
        </div>
      </div>
    </div>
  </div>
</section>
