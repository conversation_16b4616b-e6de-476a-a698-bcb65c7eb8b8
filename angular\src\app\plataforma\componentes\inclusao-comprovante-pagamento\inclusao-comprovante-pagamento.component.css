.cabecalho {
  background-color: #000646 !important;
}

.content {
  background-color: #f8f8ff;
  height: 100vh;
}

.link-pagina {
  margin-bottom: 0.1rem;
  color: #000646;
  cursor: pointer;
}

.link-item {
  cursor: pointer;
}

ul {
  margin-bottom: 0.6rem;
}

.input-group-text {
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  cursor: pointer;
}

.coluna-linha-divisao {
  border-right: 2px solid rgba(0, 0, 0, 0.1);
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.form-check-input {
  font-size: 17px;
}

.link-botao {
  text-decoration: underline;
  color: #000646;
}

.card-abas {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
  margin-left: 1px;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffecb5;
}

.coluna-esquerda {
  border-left: 2px solid rgba(0, 0, 0, 0.1);
  font-size: 80%;
}
