.titulo {
  font-size: 13px;
  max-height: 15px;
}

.icone-duplicar {
  color: #007bff;
  margin-right: 0.5rem;
  cursor: pointer;
}

.icone-continuar {
  color: #007bff;
  margin-right: 0.5rem;
  cursor: pointer;
  font-size: 1.1rem;
}

.icone-relatorio {
  color: #007bff;
  margin-right: 0.8rem;
  cursor: pointer;
}

.titulo {
  display: block;
}

.descricao {
  font-size: 12px;
}

.tabela-apuracao {
  width: 100%;
}

.tabela-apuracao:hover {
  background-color: white;
}

.tabela-filha:hover {
  background-color: white;
}

.icone-excluir-apuracao {
  color: #f44336;
  margin-right: 0.5rem;
}

.botao-abrir {
  color: #007bff !important;
  border-color: #007bff !important;
}

.botao-abrir:hover {
  color: white !important;
  background-color: #007bff !important;
}

.nav-link {
  display: block;
  padding: 0rem !important;
}

.dropdown-item,
.linha-tabela-filha {
  cursor: pointer;
}

.icone-dropdown {
  color: black;
  cursor: pointer;
}

i.far {
  color: #4d4d4f;
}

i.fas {
  color: #4d4d4f;
}

.borda-card {
  border-left: 5px solid white;
  padding-left: 0.5rem;
  border-left-color: #000646;
  border-radius: 0.25rem;
}

.icone-copia-seguranca {
  color: #000646 !important;
}

th {
  border: none;
}

.fa-clipboard {
  color: #000646 !important;
}

.fa-chart-line {
  color: #000646 !important;
}

.form-check-input:checked {
  background-color: #000646;
  border-color: #000646;
}

.coluna-acoes {
  justify-content: flex-end;
  align-items: center;
}

.input-oficial {
  margin-top: 8px;
}

.d-inline {
  display: inline;
}

.form-check-input {
  font-size: 17px;
}

.form-check-label {
  font-size: 16px;
  padding-top: 2px;
}

.fa-times {
  color: #f44336;
}

.fa-check {
  color: #28a745;
}

.fa-external-link-alt,
.link-apuracao-vinculada {
  color: #000646 !important;
}

.coluna-apelido-apuracao {
  position: relative;
}

.coluna-apelido-apuracao p {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 99%;
}

.coluna-apelido-apuracao small {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: 99%;
}

@media (max-width: 767px) {
  .table-responsive .dropdown-menu {
    position: static !important;
  }
}

@media (min-width: 768px) {
  .table-responsive {
    overflow: visible;
  }
}
