<div class="text-left pt-3 pb-1" (click)="manipularClickIconeMenu()"
  style="cursor: pointer;margin-left: 2rem;font-size: 18px;">
  <i class="fas fa-bars" style="color: #6c757d;"></i>
</div>

<!-- <nav class="mt-2">
  <ul class="nav nav-pills nav-sidebar flex-column nav-flat" data-widget="treeview" role="menu" data-accordion="false">
    <ng-container *ngFor="let item of menuItems">
      <ng-container *ngTemplateOutlet="sidebarInner; context: { item: item }"></ng-container>
    </ng-container>
  </ul>
</nav>

<ng-template #sidebarInner let-item="item">
  <li *ngIf="isMenuItemVisible(item)" class="nav-item" [class.menu-open]="!item.isCollapsed"
    [class.has-treeview]="item.children">
    <a *ngIf="item.route && item.route.indexOf('http') != 0" class="nav-link sub-item" [href]="item.route"
      [class.active]="item.isActive" data-toggle="tooltip" [title]="item.label" (click)="validarAcessoRota($event, item)">
      <i class="nav-icon {{ item.icon }}"></i>
      <p class="label-item-menu">
        {{ item.label }}
        <i id="icone-loader-modulo-{{ item.label }}" class="fa-solid fa-circle-notch fa-spin" style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"></i>
      </p>
    </a>
    <a *ngIf="item.route == null" class="nav-link" href="#"
      (click)="validarClickItemSemUrl(item); $event.preventDefault()" data-toggle="tooltip" [title]="item.label"
      [ngClass]="{'icone-alerta': item.label == 'Novidades'}">
      <i class="nav-icon {{ item.icon }}"></i>
      <p class="label-item-menu">
        {{ item.label }}
      </p>
    </a>
    <a *ngIf="item.route && item.route.indexOf('http') == 0 && !item.children" class="nav-link" target="_blank"
      [href]="item.route">
      <i class="nav-icon {{ item.icon }}"></i>
      <p class="label-item-menu">
        {{ item.label }}
      </p>
    </a>
    <a *ngIf="!item.route && item.children && possuiItemVisivelNaLista(item.children)" class="nav-link" href="javascript:;"
      (click)="item.isCollapsed = !item.isCollapsed" data-toggle="tooltip" [title]="item.label">
      <i class="nav-icon {{ item.icon }}"></i>
      <p>
        <i class="right fas fa-angle-left"></i>
        <span class="label-item-menu">{{ item.label }}</span>
      </p>
    </a>
    <ul *ngIf="item.children" class="nav nav-treeview" [collapse]="item.isCollapsed" [isAnimated]="true">
      <ng-container *ngFor="let item of item.children">
        <ng-container *ngTemplateOutlet="sidebarInner; context: { item: item }"></ng-container>
      </ng-container>
    </ul>
  </li>
</ng-template> -->

{{ statusMenuLateral }}

<nav class="mt-2">
  <ul data-widget="treeview" role="menu" data-accordion="false" class="nav nav-pills nav-sidebar flex-column nav-flat">
    <li class="nav-item">
      <a data-toggle="tooltip" class="nav-link sub-item active" [href]="tratarAppPathDaRota('/app/plataforma')"
        title="Home">
        <i class="fa-home fas nav-icon"></i>
        <p class="label-item-menu" style="display: inline;"> Home <i class="fa-solid fa-circle-notch fa-spin"
            style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
            id="icone-loader-modulo-Home"></i></p>
      </a>
    </li>
    <li class="nav-item mt-2">
      <p class="pl-4" style="display: inline; opacity: .6;">Módulos</p>
      <hr class="ml-4 mb-0 mt-0">
    </li>
    <li class="nav-item">
      <a data-toggle="tooltip" class="nav-link sub-item" [href]="tratarAppPathDaRota('/diretos/home')"
        title="TS - Diretos">
        <i class="fa-funnel-dollar fas nav-icon"></i>
        <p class="label-item-menu" style="display: inline;"> TS - Diretos <i class="fa-solid fa-circle-notch fa-spin"
            style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
            id="icone-loader-modulo-TS - Diretos"></i></p>
      </a>
    </li>
    <li class="nav-item">
      <a data-toggle="tooltip" class="nav-link sub-item" [href]="tratarAppPathDaRota('/drive/home')" title="TS - Drive">
        <i class="fa-hdd fas nav-icon"></i>
        <p class="label-item-menu" style="display: inline;"> TS - Drive <i class="fa-solid fa-circle-notch fa-spin"
            style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
            id="icone-loader-modulo-TS - Drive"></i></p>
      </a>
    </li>
    <li class="nav-item mt-2">
      <p class="pl-4" style="display: inline; opacity: .6;">Configurações</p>
      <hr class="ml-4 mb-0 mt-0">
    </li>
    <li class="nav-item has-treeview">
      <a class="nav-link" href="javascript:;" (click)="collapseSubMenuConta = !collapseSubMenuConta"
        data-toggle="tooltip">
        <i class="fa-cog fas nav-icon"></i>
        <p>
          <i *ngIf="collapseSubMenuConta" class="right fas fa-angle-down"></i>
          <i *ngIf="!collapseSubMenuConta" class="right fas fa-angle-up"></i>
          <span class="label-item-menu" style="display: inline;">Conta</span>
        </p>
      </a>
      <ul class="nav nav-treeview" [collapse]="collapseSubMenuConta" [isAnimated]="true">
        <li class="nav-item">
          <a data-toggle="tooltip" class="nav-link sub-item" [href]="tratarAppPathDaRota('/app/plataforma/users')"
            title="Usuários" style="padding-left: 2rem;">
            <i class="fa-users fas nav-icon"></i>
            <p class="label-item-menu" style="display: inline;"> Usuários <i class="fa-solid fa-circle-notch fa-spin"
                style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
                id="icone-loader-modulo-Usuários"></i></p>
          </a>
        </li>
        <li class="nav-item">
          <a data-toggle="tooltip" class="nav-link sub-item" [href]="tratarAppPathDaRota('/app/plataforma/roles')"
            title="Perfis" style="padding-left: 2rem;">
            <i class="fa-theater-masks fas nav-icon"></i>
            <p class="label-item-menu" style="display: inline;"> Perfis <i class="fa-solid fa-circle-notch fa-spin"
                style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
                id="icone-loader-modulo-Perfis"></i></p>
          </a>
        </li>
        <li class="nav-item">
          <a data-toggle="tooltip" class="nav-link sub-item" [href]="tratarAppPathDaRota('/app/plataforma/empresas')"
            title="CNPJs" style="padding-left: 2rem;">
            <i class="fa-industry fas nav-icon"></i>
            <p class="label-item-menu" style="display: inline;"> CNPJs <i class="fa-solid fa-circle-notch fa-spin"
                style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
                id="icone-loader-modulo-CNPJs"></i></p>
          </a>
        </li>
        <li class="nav-item">
          <a data-toggle="tooltip" class="nav-link sub-item"
            [href]="tratarAppPathDaRota('/app/plataforma/configurar-tenant')" title="Configurações"
            style="padding-left: 2rem;">
            <i class="fa-building fas nav-icon"></i>
            <p class="label-item-menu" style="display: inline;"> Configurações <i
                class="fa-solid fa-circle-notch fa-spin"
                style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
                id="icone-loader-modulo-Configurações"></i></p>
          </a>
        </li>
        <li class="nav-item">
          <a data-toggle="tooltip" class="nav-link sub-item"
            [href]="tratarAppPathDaRota('/app/plataforma/acesso-empresas')" title="Acesso às empresas"
            style="padding-left: 2rem;">
            <i class="fa-door-open fas nav-icon"></i>
            <p class="label-item-menu" style="display: inline;"> Acesso às empresas <i
                class="fa-solid fa-circle-notch fa-spin"
                style="animation-duration: var(--fa-animation-duration,2s) !important; display: none;"
                id="icone-loader-modulo-Acesso às empresas"></i></p>
          </a>
        </li>
      </ul>
    </li>
    <li class="nav-item mt-2">
      <p class="pl-4" style="display: inline; opacity: .6;">Suporte</p>
      <hr class="ml-4 mb-0 mt-0">
    </li>
    <li class="nav-item">
      <a href="#" data-toggle="tooltip" class="nav-link" title="Ajuda" ng-reflect-ng-class="[object Object]">
        <i class="fa-headset fas nav-icon"></i>
        <p class="label-item-menu" style="display: inline;"> Ajuda </p>
      </a>
    </li>
  </ul>
</nav>