﻿<form
  class="form-horizontal"
  autocomplete="off"
  #Form="ngForm"
  [formGroup]="mfaForm"
  [busy]="carregando"
>
  <div class="form-row mb-3" *ngIf="MFAComAplicativo">
    <div class="col-8 text-center">
      <img width="50%" height="auto" [src]="imageQRCodePath" />
      <p>Faça o download do app utilizando os códigos ao lado</p>
    </div>
    <div class="col-4 mt-4 text-center">
      <img
        src="assets/Tax/link_app_google_authenticator.png"
        style="vertical-align: bottom"
        width="25%"
        height="auto"
      />
      <h6>Google Authenticator</h6>
      <img
        src="assets/Tax/link_app_ms_authenticator.png"
        style="vertical-align: bottom"
        width="25%"
        height="auto"
        class="mt-3"
      />
      <h6>Microsoft Authenticator</h6>
    </div>
  </div>
  <div class="form-row">
    <h5 class="pl-2 pt-3 pr-3">PIN:</h5>
    <div class="form-group alert alert-secondary p-2">
      <input
        type="text"
        (keypress)="validarValorDigitado($event)"
        (keydown.space)="$event.preventDefault()"
        class="partitioned"
        name="codigo"
        id="codigo"
        required
        formControlName="codigo"
        (paste)="validarValorColado($event)"
        #codigo
      />
    </div>
  </div>
  <h6 class="mt-2 ml-3 text-center" style="color: #000646; width: 60%">
    {{ getMessageMfa() }}
  </h6>
</form>
