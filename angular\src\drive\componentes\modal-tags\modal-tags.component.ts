import {
  Component,
  ElementRef,
  EventEmitter,
  Injector,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { DriveService } from "@drive/servicos-drive/drive-server";
import { TagService } from "@drive/servicos-drive/tag-server";
import { AppComponentBase } from "@shared/app-component-base";
import { DiretorioTagCollection } from "@shared/drive/entidades/drive";
import { CriarTag, Tag } from "@shared/drive/entidades/tag";
import { BsModalRef } from "ngx-bootstrap/modal";

@Component({
  selector: "app-modal-tags",
  templateUrl: "./modal-tags.component.html",
  styleUrls: ["./modal-tags.component.css"],
})
export class ModalTagsComponent extends AppComponentBase implements OnInit {
  @Output() eventoDadosAlterados = new EventEmitter<any>();
  _carregando = false;
  diretorio;
  tag: Tag;
  _editar: boolean = false;
  cnpj: number;
  _adicionar: boolean = false;
  tagFormulario: FormGroup;

  coresPadroes: any[] = [
    { corTag: "#000646", corFonte: "#4D4D4F" },
    { corTag: "#C5953C", corFonte: "#4D4D4F" },
    { corTag: "#FFC96B", corFonte: "#4D4D4F" },
    { corTag: "#FFB994", corFonte: "#4D4D4F" },
    { corTag: "#FFE23C", corFonte: "#4D4D4F" },
    { corTag: "#F4F3CC", corFonte: "#4D4D4F" },
    { corTag: "#DBF9D8", corFonte: "#4D4D4F" },
    { corTag: "#8CB788", corFonte: "#4D4D4F" },
    { corTag: "#909133", corFonte: "#FFFFFF" },
    { corTag: "#008B25", corFonte: "#FFFFFF" },
    { corTag: "#5A893D", corFonte: "#FFFFFF" },
    { corTag: "#167D4E", corFonte: "#FFFFFF" },
    { corTag: "#86AEE1", corFonte: "#4D4D4F" },
    { corTag: "#0097FF", corFonte: "#FFFFFF" },
    { corTag: "#2F4858", corFonte: "#FFFFFF" },
    { corTag: "#3E4A3D", corFonte: "#FFFFFF" },
    { corTag: "#A1AF9F", corFonte: "#4D4D4F" },
    { corTag: "#0080ED", corFonte: "#FFFFFF" },
    { corTag: "#0050BA", corFonte: "#FFFFFF" },
    { corTag: "#FF5F4E", corFonte: "#FFFFFF" },
    { corTag: "#D73B31", corFonte: "#FFFFFF" },
    { corTag: "#E6767A", corFonte: "#FFFFFF" },
    { corTag: "#BA6790", corFonte: "#FFFFFF" },
    { corTag: "#806190", corFonte: "#FFFFFF" },
  ];
  selectedTags: Tag[] = [];
  registros: Tag[] = [];

  @ViewChild("inputFilter") inputFilterRef: ElementRef;
  constructor(
    injector: Injector,
    public bsModalRef: BsModalRef,
    protected readonly fb: FormBuilder,
    private _tagService: TagService,
    private _driveService: DriveService
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.tagFormulario = this.fb.group({
      id: [""],
      descricao: ["", Validators.required],
      corTag: ["#000646", Validators.required],
      corFonte: ["#000000", Validators.required],
      selecionado: [""],
      filtro: [""],
    });

    this.buscarRegistroParaEdicao();
    setTimeout(() => {
      this.inputFilterRef.nativeElement.focus();
    }, 500);
  }

  registrosFiltrados(): Tag[] {
    if (this.tagFormulario.value.filtro)
      return this.registros.filter((f) =>
        f.descricao
          .toLowerCase()
          .includes(this.tagFormulario.value.filtro?.toLowerCase())
      );
    return this.registros;
  }

  editarRegistro(tag: any): void {
    this._carregando = true;
    if (this.tagFormulario.valid)
      this._tagService
        .editar(this.tagFormulario.value)
        .toPromise()
        .then(() => {
          this.notify.info(this.l("Tag atualziada com sucesso"));
          this.eventoDadosAlterados.emit();
          this.buscarRegistroParaEdicao();
        })
        .catch((erro) => {
          this.notify.error(erro.error.error.message);
        })
        .finally(() => {
          this._carregando = false;
          this._editar = false;
          this.tagFormulario.reset();
        });
  }

  salvarRegistro(T: any): void {
    this._carregando = true;
    if (this.tagFormulario.valid)
      this._tagService
        .salvar(new CriarTag(this.tagFormulario.value))
        .toPromise()
        .then(() => {
          this.notify.info(this.l("Tag cadastrada com sucesso"));
          this.eventoDadosAlterados.emit();
          this.buscarRegistroParaEdicao();
          this._carregando = false;
          this._adicionar = false;
          this.tagFormulario.reset();
        })
        .catch((erro) => {
          this._carregando = false;
          this.notify.error(erro.error.error.message);
        });
  }

  excluirRegistro(tag: Tag) {
    this._carregando = true;
    let index = this.registros.findIndex((obj) => obj.id === tag.id);
    if (index !== -1) {
      this._tagService
        .excluir(tag.id)
        .toPromise()
        .then(() => {
          this.notify.info(this.l("Tag atualziada com sucesso"));
          this.eventoDadosAlterados.emit();
          this.buscarRegistroParaEdicao();
        })
        .catch((erro) => {
          this.notify.error(erro.error.error.message);
        })
        .finally(() => {
          this._carregando = false;
          this._editar = false;
          this.tagFormulario.reset();
        });
    }
  }
  buscarRegistroParaEdicao(): void {
    this._tagService
      .getAll()
      .toPromise()
      .then((data) => {
        if (data.items) {
          if (this.diretorio.length > 1) {
            const filteredUniqueTags = [
              ...new Set(
                this.diretorio
                  .map((x) => x.diretorioTagCollection || [])
                  .flat()
                  .map((x) => x.tagId)
              ),
            ];
            this.registros = data.items
              .sort((a, b) =>
                a.descricao.toLowerCase() < b.descricao.toLowerCase() ? -1 : 1
              )
              .map((x) => {
                x.selecionado = filteredUniqueTags.includes(x.id);
                return x;
              });
          } else {
            this.registros = data.items
              .sort((a, b) =>
                a.descricao.toLowerCase() < b.descricao.toLowerCase() ? -1 : 1
              )
              .map((x) => {
                x.selecionado = (
                  this.diretorio[0].diretorioTagCollection || []
                ).some((s) => s.tagId == x.id);
                return x;
              });
          }
        }
      })
      .catch((erro) => {
        this.notify.error(erro.error.error.message);
      })
      .finally(() => {
        this._editar = false;
        this.tagFormulario.reset();
      });
  }

  tituloModal() {
    if (this._editar) return "Editar Tag";
    if (this._adicionar) return "Adicionar Tag";
    return "Tags";
  }

  labelBotaoSecundario() {
    return this._editar ? "" : "Cancelar";
  }

  labelBotaoPrincipal() {
    if (this._editar || this._adicionar) return "Salvar";
    return "Adicionar Tag";
  }

  selecionarCor(corSelecionada): void {
    this.tagFormulario.get("corTag").setValue(corSelecionada.corTag);
    this.tagFormulario.get("corFonte").setValue(corSelecionada.corFonte);
  }

  submitFormulario(): void {
    if (this._editar) {
      let tag = new Tag(this.tagFormulario.value);
      this.editarRegistro(tag);
      return;
    }
    if (this._adicionar) {
      let tag = new Tag(this.tagFormulario.value);
      this.salvarRegistro(tag);
      return;
    }

    this._adicionar = true;
    this.tagFormulario.reset();
    this.tagFormulario.get("corTag").setValue("#000646");
    this.tagFormulario.get("corFonte").setValue("#000000");
    setTimeout(() => {
      document.getElementById("campo-descricao").focus();
    }, 100);
  }

  voltar() {
    this._adicionar = false;
    this._editar = false;
  }

  apresentarFomulario() {
    return this._adicionar || this._editar;
  }

  editar(tag: Tag) {
    this._editar = true;
    this.tagFormulario.patchValue(tag);
    setTimeout(() => {
      document.getElementById("campo-descricao").focus();
    }, 100);
  }

  cancelar() {
    if (this._adicionar) {
      this._adicionar = false;
      return;
    }
    this.bsModalRef.hide();
  }

  excluir() {
    let tag = new Tag(this.tagFormulario.value);
    abp.message.confirm(
      `A tag ${tag.descricao} será excluída`,
      undefined,
      (result: boolean) => {
        if (result) {
          this.excluirRegistro(tag);
          this.buscarRegistroParaEdicao();
        }
      }
    );
  }

  onCheckTag(tag: Tag) {
    this._carregando = true;
    if (!tag.selecionado) {
      if (this.diretorio.length > 1) {
        for (let i = 0; i < this.diretorio.length; i++) {
          let diretorioTag = new DiretorioTagCollection({
            tagConpartilhado: tag.compartilhado,
            tagId: tag.id,
            tagDescricao: tag.descricao,
            tagCorFonte: tag.corFonte,
            tagCorTag: tag.corTag,
            tenantId: this.diretorio[i].tenantId,
            diretorioId: this.diretorio[i].id,
          });
          this._driveService
            .criarDiretorioTag(diretorioTag)
            .toPromise()
            .then(() => {
              this.diretorio[i].diretorioTagCollection.push(diretorioTag);
              this.eventoDadosAlterados.emit();
            })
            .catch((erro) => {
              this.notify.error(erro.error.error.message);
            })
            .finally(() => (this._carregando = false));
        }
        tag.selecionado = !tag.selecionado;
      } else {
        let diretorioTag = new DiretorioTagCollection({
          tagConpartilhado: tag.compartilhado,
          tagId: tag.id,
          tagDescricao: tag.descricao,
          tagCorFonte: tag.corFonte,
          tagCorTag: tag.corTag,
          tenantId: this.diretorio[0].tenantId,
          diretorioId: this.diretorio[0].id,
        });
        this._driveService
          .criarDiretorioTag(diretorioTag)
          .toPromise()
          .then(() => {
            this.diretorio[0].diretorioTagCollection.push(diretorioTag);
            this.eventoDadosAlterados.emit();
            tag.selecionado = !tag.selecionado;
          })
          .catch((erro) => {
            this.notify.error(erro.error.error.message);
          })
          .finally(() => (this._carregando = false));
      }
    } else {
      if (this.diretorio.length > 1) {
        for (let i = 0; i < this.diretorio.length; i++) {
          if (
            this.diretorio[i].diretorioTagCollection
              .map((x) => x.tagId)
              .includes(tag.id)
          ) {
            this._driveService
              .excluirDiretorioTag(this.diretorio[i].id, tag.id)
              .toPromise()
              .then(() => {
                this.diretorio[i].diretorioTagCollection = this.diretorio[
                  i
                ].diretorioTagCollection.filter((f) => f.tagId != tag.id);
                this.eventoDadosAlterados.emit();
              })
              .catch((erro) => {
                this.notify.error(erro.error.error.message);
              })
              .finally(() => (this._carregando = false));
          }
        }
        tag.selecionado = !tag.selecionado;
      } else {
        this._driveService
          .excluirDiretorioTag(this.diretorio[0].id, tag.id)
          .toPromise()
          .then(() => {
            this.diretorio[0].diretorioTagCollection =
              this.diretorio[0].diretorioTagCollection.filter(
                (f) => f.tagId != tag.id
              );
            this.eventoDadosAlterados.emit();
            tag.selecionado = !tag.selecionado;
          })
          .catch((erro) => {
            this.notify.error(erro.error.error.message);
          })
          .finally(() => (this._carregando = false));
      }
    }
  }
}
