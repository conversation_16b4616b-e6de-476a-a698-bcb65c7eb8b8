<form class="form-horizontal" autocomplete="off" (ngSubmit)="salvar()">
  <section class="content px-2">
    <div class="container-fluid">
      <div class="card mt-2 p-4">
        <div class="nav">
          <p class="pr-2 voltar" (click)="voltar()">Perfis</p>
          /
          <p class="desativado pl-2">{{ role.displayName }}</p>
        </div>
        <div class="titulo-content">
          <h4>Informações de Cadastro</h4>
          <hr class="my-1" />
        </div>
        <div class="content-form">
          <div class="form-row">
            <div class="form-group col-6 required">
              <label for="name">Nome completo</label>
              <input
                type="text"
                class="form-control form-control-sm"
                name="name"
                id="name"
                required
                minlength="2"
                placeholder="Nome do perfil"
                maxlength="32"
                [(ngModel)]="role.name"
                #nameModel="ngModel"
              />
            </div>
            <div class="form-group col-6 required">
              <label for="displayName">{{ "DisplayName" | localize }}</label>
              <input
                type="text"
                class="form-control form-control-sm"
                name="displayName"
                id="displayName"
                placeholder="Nome de exibição"
                required
                minlength="2"
                maxlength="32"
                [(ngModel)]="role.displayName"
                #displayNameModel="ngModel"
              />
            </div>
          </div>
          <div class="form-row">
            <div class="form-group col-12 mb-0">
              <label for="description">{{
                "RoleDescription" | localize
              }}</label>
              <textarea
                class="form-control form-control-sm"
                name="description"
                placeholder="Descrição do perfil"
                id="description"
                [(ngModel)]="role.description"
              >
              </textarea>
            </div>
          </div>
        </div>
        <div class="titulo-content mt-4">
          <h4>Permissões</h4>
          <hr class="my-1" />
        </div>
        <div class="content-form">
          <div *ngFor="let modulo of permissions">
            <div class="modulo-item pb-2">
              <div class="titulo row" (click)="toggleModulo(modulo?.modulo)">
                <p class="col-10 mb-0 mt-1 texto-modulo">
                  {{ modulo.descricao }}
                </p>
                <div class="col-2 flex">
                  <div *ngIf="ativo.includes(modulo?.modulo)">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24px"
                      height="24px"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M18.7071 5.29289C19.0976 5.68342 19.0976 6.31658 18.7071 6.70711L13.4142 12L18.7071 17.2929C19.0976 17.6834 19.0976 18.3166 18.7071 18.7071C18.3166 19.0976 17.6834 19.0976 17.2929 18.7071L12 13.4142L6.70711 18.7071C6.31658 19.0976 5.68342 19.0976 5.29289 18.7071C4.90237 18.3166 4.90237 17.6834 5.29289 17.2929L10.5858 12L5.29289 6.70711C4.90237 6.31658 4.90237 5.68342 5.29289 5.29289C5.68342 4.90237 6.31658 4.90237 6.70711 5.29289L12 10.5858L17.2929 5.29289C17.6834 4.90237 18.3166 4.90237 18.7071 5.29289Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                  <div *ngIf="!ativo.includes(modulo?.modulo)">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24px"
                      height="24px"
                      viewBox="0 0 24 24"
                      fill="none"
                    >
                      <path
                        d="M5.70711 9.71069C5.31658 10.1012 5.31658 10.7344 5.70711 11.1249L10.5993 16.0123C11.3805 16.7927 12.6463 16.7924 13.4271 16.0117L18.3174 11.1213C18.708 10.7308 18.708 10.0976 18.3174 9.70708C17.9269 9.31655 17.2937 9.31655 16.9032 9.70708L12.7176 13.8927C12.3271 14.2833 11.6939 14.2832 11.3034 13.8927L7.12132 9.71069C6.7308 9.32016 6.09763 9.32016 5.70711 9.71069Z"
                        fill="currentColor"
                      />
                    </svg>
                  </div>
                </div>
              </div>
              <div
                *ngIf="ativo.includes(modulo.modulo)"
                class="form-group row mb-0 px-4 accordion-content borda-topo pb-2"
              >
                <div class="form-check form-switch my-2 ml-3">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    role="switch"
                    [id]="'selecionarTodos' + modulo.modulo"
                    [name]="'selecionarTodos' + modulo.modulo"
                    title="Selecionar todas as permissões"
                    [checked]="estaoSelecionados(modulo)"
                    (change)="selecionarTodos(modulo)"
                    style="accent-color: #000646"
                  />
                  <label
                    class="form-check-label pt-1 text-bold"
                    [for]="'selecionarTodos' + modulo.modulo"
                    >Selecionar todas as permissões</label
                  >
                </div>
                <h6 class="pl-0 ml-0 pb-3 mb-3 text-bold border-bottom"></h6>
                <div
                  class="col-md-6 pl-1 spacing-column"
                  *ngFor="let permissao of modulo.permissoes"
                >
                  <div class="row">
                    <div class="col-8">
                      <p>
                        {{ permissao.descricao }}
                      </p>
                    </div>
                    <div class="col-4">
                      <select
                        [id]="'permissionId_' + permissao.descricao"
                        [name]="'permission_' + permissao.descricao"
                        title="Escolha as permissões"
                        class="form-control form-control-sm form-select"
                        [(ngModel)]="permissao.value"
                      >
                        <option
                          *ngFor="let acesso of permissao.acessosPossiveis"
                          [value]="acesso.descricao"
                          [attr.key]="
                            permissao.descricao + '_key_' + acesso.descricao
                          "
                        >
                          {{ converterNome(acesso.descricao) }}
                        </option>
                      </select>
                    </div>
                  </div>
                </div>
                <div
                  *ngFor="let submodulo of modulo.subModulos"
                  class="mb-0 p-0"
                >
                  <div class="form-check form-switch mb-3 ml-3 mt-4">
                    <input
                      class="form-check-input d-block"
                      type="checkbox"
                      role="switch"
                      [id]="submodulo.nome + 'switch'"
                      [name]="submodulo.nome + 'switch'"
                      title="Selecionar todas as permissões"
                      [checked]="submodulo.value == 'ComAcesso'"
                      (change)="permitirSubModulo(submodulo)"
                      style="accent-color: #000646"
                    />
                    <label
                      class="form-check-label pt-1 text-bold"
                      [for]="submodulo.nome + 'switch'"
                    >
                      {{ submodulo.nome }}
                    </label>
                  </div>
                  <div class="row mb-0 p-0 pt-3 border-top">
                    <div
                      class="col-md-6 pl-1 spacing-column"
                      *ngFor="let subPermissao of submodulo.permissoes"
                    >
                      <div class="row">
                        <div class="col-8">
                          <p
                            [ngClass]="{
                              'text-muted': submodulo.value != 'ComAcesso'
                            }"
                          >
                            {{ subPermissao.descricao }}
                          </p>
                        </div>
                        <div class="col-4">
                          <select
                            [id]="'permissionId_' + subPermissao.descricao"
                            [name]="'permission_' + subPermissao.descricao"
                            title="Escolha as permissões"
                            class="form-control form-control-sm form-select"
                            [(ngModel)]="subPermissao.value"
                            [disabled]="submodulo.value != 'ComAcesso'"
                          >
                            <option
                              *ngFor="
                                let acesso of subPermissao.acessosPossiveis;
                                trackBy: trackByFn
                              "
                              [value]="acesso.descricao"
                              [attr.key]="
                                subPermissao.descricao +
                                '_sub_key_' +
                                acesso.descricao
                              "
                            >
                              {{ converterNome(acesso.descricao) }}
                            </option>
                          </select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group mt-4">
          <button type="submit" class="btn btn-primary w-100">
            Salvar Alterações
          </button>
        </div>
      </div>
    </div>
  </section>
</form>
