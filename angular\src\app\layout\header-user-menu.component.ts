import {
  ChangeDetectionStrategy,
  Component,
  Injector,
  OnInit,
} from "@angular/core";
import { AppComponentBase } from "@shared/app-component-base";
import { AppAuthService } from "@shared/auth/app-auth.service";
import { AppSessionService } from "@shared/session/app-session.service";

@Component({
  selector: "header-user-menu",
  templateUrl: "./header-user-menu.component.html",
  styleUrls: ["./header-user-menu.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HeaderUserMenuComponent
  extends AppComponentBase
  implements OnInit
{
  loginNameTratado = "";

  constructor(
    injector: Injector,
    private _sessionService: AppSessionService,
    private _authService: AppAuthService
  ) {
    super(injector);
  }

  ngOnInit(): void {
    this.loginNameTratado = this.appSession.getShownLoginName();
  }

  logout(): void {
    this._authService.logout(
      this._sessionService.tipoLogin,
      this._sessionService.tenantId,
      this._sessionService.userId
    );
  }
}
